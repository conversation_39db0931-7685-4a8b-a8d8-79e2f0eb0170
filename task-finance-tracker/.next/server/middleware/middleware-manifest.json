{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_d7c7841b._.js", "server/edge/chunks/[root-of-the-server]__c28bd568._.js", "server/edge/chunks/edge-wrapper_c91542f8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|login).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|login).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "XZkPj/0feIj7kiVEsbrkntKD6r2uXNrs5V/sRi5Jk6M=", "__NEXT_PREVIEW_MODE_ID": "d6123d7a9de754e64ff9c616cd7111c6", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "28f6f4b98c246a1d14155b3d766a554006729e5b0ca629f83da046cb8895c949", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2e1e05d79b0d6dbb4da1cd1b78e48f7eb0e35c45a1410e8a9d7f724167918a30"}}}, "instrumentation": null, "functions": {}}