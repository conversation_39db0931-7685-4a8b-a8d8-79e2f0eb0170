{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_d7c7841b._.js", "server/edge/chunks/[root-of-the-server]__c28bd568._.js", "server/edge/chunks/edge-wrapper_c91542f8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|login).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|login).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "XZkPj/0feIj7kiVEsbrkntKD6r2uXNrs5V/sRi5Jk6M=", "__NEXT_PREVIEW_MODE_ID": "7642d485acea1db67de4f3b177b232f0", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "95898253d5b68680e673b935dc934827b7766d661e3cd5e5dfa2b511647c0667", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "967f3b99d8f82a4a5eac11c9508a840c039f0dd2b9d7551a337a0f13a12008f3"}}}, "instrumentation": null, "functions": {}}