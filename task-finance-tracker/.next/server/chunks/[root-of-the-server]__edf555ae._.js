module.exports = {

"[project]/.next-internal/server/app/api/tasks/hierarchy/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/node:crypto [external] (node:crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}}),
"[project]/src/lib/kv.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "KVClient": (()=>KVClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@vercel/kv/dist/index.js [app-route] (ecmascript)");
;
// 開発環境かどうかを判定
const isDevelopment = ("TURBOPACK compile-time value", "development") === 'development';
const hasKVConfig = process.env.KV_URL || process.env.KV_REST_API_URL;
class KVClient {
    // タスク関連のキー
    static TASKS_KEY = 'tasks';
    static TASK_COUNTER_KEY = 'task_counter';
    // 収支関連のキー
    static FINANCIAL_PLANS_KEY = 'financial_plans';
    static FINANCIAL_COUNTER_KEY = 'financial_counter';
    // タスクデータの取得
    static async getTasks() {
        try {
            const tasks = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].get(this.TASKS_KEY);
            return tasks || [];
        } catch (error) {
            console.error('Error getting tasks:', error);
            return [];
        }
    }
    // タスクデータの保存
    static async saveTasks(tasks) {
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].set(this.TASKS_KEY, tasks);
        } catch (error) {
            console.error('Error saving tasks:', error);
            throw error;
        }
    }
    // 新しいタスクIDの生成
    static async getNextTaskId() {
        try {
            const counter = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].incr(this.TASK_COUNTER_KEY);
            return `task_${counter}`;
        } catch (error) {
            console.error('Error generating task ID:', error);
            throw error;
        }
    }
    // 収支計画データの取得
    static async getFinancialPlans() {
        try {
            const plans = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].get(this.FINANCIAL_PLANS_KEY);
            return plans || [];
        } catch (error) {
            console.error('Error getting financial plans:', error);
            return [];
        }
    }
    // 収支計画データの保存
    static async saveFinancialPlans(plans) {
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].set(this.FINANCIAL_PLANS_KEY, plans);
        } catch (error) {
            console.error('Error saving financial plans:', error);
            throw error;
        }
    }
    // 新しい収支計画IDの生成
    static async getNextFinancialId() {
        try {
            const counter = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].incr(this.FINANCIAL_COUNTER_KEY);
            return `financial_${counter}`;
        } catch (error) {
            console.error('Error generating financial ID:', error);
            throw error;
        }
    }
    // 開発用：全データのクリア
    static async clearAllData() {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        try {
            await Promise.all([
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].del(this.TASKS_KEY),
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].del(this.TASK_COUNTER_KEY),
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].del(this.FINANCIAL_PLANS_KEY),
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].del(this.FINANCIAL_COUNTER_KEY)
            ]);
        } catch (error) {
            console.error('Error clearing data:', error);
            throw error;
        }
    }
    // ヘルスチェック
    static async healthCheck() {
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].set('health_check', Date.now(), {
                ex: 60
            }); // 60秒で期限切れ
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].get('health_check');
            return result !== null;
        } catch (error) {
            console.error('KV health check failed:', error);
            return false;
        }
    }
}
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "checkAuthMiddleware": (()=>checkAuthMiddleware),
    "createSession": (()=>createSession),
    "destroySession": (()=>destroySession),
    "validateLogin": (()=>validateLogin),
    "validateSession": (()=>validateSession)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
;
// 認証設定
const AUTH_COOKIE_NAME = 'auth-session';
const SESSION_DURATION = 24 * 60 * 60 * 1000; // 24時間
// 環境変数から認証情報を取得
const getAuthCredentials = ()=>{
    const username = process.env.AUTH_USERNAME;
    const password = process.env.AUTH_PASSWORD;
    if (!username || !password) {
        throw new Error('AUTH_USERNAME and AUTH_PASSWORD must be set in environment variables');
    }
    return {
        username,
        password
    };
};
const validateLogin = (username, password)=>{
    try {
        const credentials = getAuthCredentials();
        return username === credentials.username && password === credentials.password;
    } catch (error) {
        console.error('Auth validation error:', error);
        return false;
    }
};
const createSession = async ()=>{
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    const sessionData = {
        authenticated: true,
        timestamp: Date.now()
    };
    cookieStore.set(AUTH_COOKIE_NAME, JSON.stringify(sessionData), {
        httpOnly: true,
        secure: ("TURBOPACK compile-time value", "development") === 'production',
        sameSite: 'lax',
        maxAge: SESSION_DURATION / 1000
    });
};
const validateSession = async ()=>{
    try {
        const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
        const sessionCookie = cookieStore.get(AUTH_COOKIE_NAME);
        if (!sessionCookie) {
            return false;
        }
        const sessionData = JSON.parse(sessionCookie.value);
        const now = Date.now();
        // セッションの有効期限チェック
        if (now - sessionData.timestamp > SESSION_DURATION) {
            return false;
        }
        return sessionData.authenticated === true;
    } catch (error) {
        console.error('Session validation error:', error);
        return false;
    }
};
const destroySession = async ()=>{
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    cookieStore.delete(AUTH_COOKIE_NAME);
};
const checkAuthMiddleware = async (request)=>{
    const { pathname } = request.nextUrl;
    // ログインページとAPIルートは除外
    if (pathname === '/login' || pathname.startsWith('/api/auth')) {
        return null;
    }
    try {
        const sessionCookie = request.cookies.get(AUTH_COOKIE_NAME);
        if (!sessionCookie) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/login', request.url));
        }
        const sessionData = JSON.parse(sessionCookie.value);
        const now = Date.now();
        // セッションの有効期限チェック
        if (now - sessionData.timestamp > SESSION_DURATION || !sessionData.authenticated) {
            const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/login', request.url));
            response.cookies.delete(AUTH_COOKIE_NAME);
            return response;
        }
        return null; // 認証OK、処理続行
    } catch (error) {
        console.error('Middleware auth error:', error);
        const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/login', request.url));
        response.cookies.delete(AUTH_COOKIE_NAME);
        return response;
    }
};
}}),
"[project]/src/app/api/tasks/hierarchy/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$kv$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/kv.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
;
;
;
async function GET(request) {
    try {
        // 認証チェック
        const isAuthenticated = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateSession"])();
        if (!isAuthenticated) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: '認証が必要です'
            }, {
                status: 401
            });
        }
        const tasks = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$kv$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["KVClient"].getTasks();
        // 階層構造を構築
        const taskMap = new Map();
        const rootTasks = [];
        // 全タスクをマップに追加
        tasks.forEach((task)=>{
            taskMap.set(task.id, {
                ...task,
                children: []
            });
        });
        // 階層構造を構築
        tasks.forEach((task)=>{
            const taskWithChildren = taskMap.get(task.id);
            if (task.parentId) {
                const parent = taskMap.get(task.parentId);
                if (parent) {
                    parent.children.push(taskWithChildren);
                } else {
                    // 親が見つからない場合はルートタスクとして扱う
                    rootTasks.push(taskWithChildren);
                }
            } else {
                rootTasks.push(taskWithChildren);
            }
        });
        // 各レベルで作成日時順にソート
        const sortTasks = (tasks)=>{
            tasks.sort((a, b)=>new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
            tasks.forEach((task)=>{
                if (task.children.length > 0) {
                    sortTasks(task.children);
                }
            });
        };
        sortTasks(rootTasks);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            tasks: rootTasks
        });
    } catch (error) {
        console.error('Error fetching task hierarchy:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'タスク階層の取得に失敗しました'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        // 認証チェック
        const isAuthenticated = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateSession"])();
        if (!isAuthenticated) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: '認証が必要です'
            }, {
                status: 401
            });
        }
        const tasks = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$kv$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["KVClient"].getTasks();
        // 全体統計
        const totalTasks = tasks.length;
        const completedTasks = tasks.filter((t)=>t.status === 'completed').length;
        const inProgressTasks = tasks.filter((t)=>t.status === 'in_progress').length;
        const notStartedTasks = tasks.filter((t)=>t.status === 'not_started').length;
        // 親タスク別統計
        const parentStats = new Map();
        // ルートタスクの統計
        const rootTasks = tasks.filter((t)=>!t.parentId);
        rootTasks.forEach((rootTask)=>{
            const childTasks = tasks.filter((t)=>t.parentId === rootTask.id);
            const total = childTasks.length;
            const completed = childTasks.filter((t)=>t.status === 'completed').length;
            const inProgress = childTasks.filter((t)=>t.status === 'in_progress').length;
            const notStarted = childTasks.filter((t)=>t.status === 'not_started').length;
            parentStats.set(rootTask.id, {
                total,
                completed,
                inProgress,
                notStarted,
                title: rootTask.title
            });
        });
        // 優先度別統計
        const priorityStats = {
            high: tasks.filter((t)=>t.priority === 'high').length,
            medium: tasks.filter((t)=>t.priority === 'medium').length,
            low: tasks.filter((t)=>t.priority === 'low').length
        };
        // 期限別統計（今月、来月、それ以降）
        const now = new Date();
        const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
        const monthAfterNext = new Date(now.getFullYear(), now.getMonth() + 2, 1);
        const dueDateStats = {
            thisMonth: tasks.filter((t)=>{
                if (!t.targetDate) return false;
                const targetDate = new Date(t.targetDate);
                return targetDate >= thisMonth && targetDate < nextMonth;
            }).length,
            nextMonth: tasks.filter((t)=>{
                if (!t.targetDate) return false;
                const targetDate = new Date(t.targetDate);
                return targetDate >= nextMonth && targetDate < monthAfterNext;
            }).length,
            later: tasks.filter((t)=>{
                if (!t.targetDate) return false;
                const targetDate = new Date(t.targetDate);
                return targetDate >= monthAfterNext;
            }).length,
            noDate: tasks.filter((t)=>!t.targetDate).length
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            overall: {
                total: totalTasks,
                completed: completedTasks,
                inProgress: inProgressTasks,
                notStarted: notStartedTasks,
                completionRate: totalTasks > 0 ? Math.round(completedTasks / totalTasks * 100) : 0
            },
            byParent: Object.fromEntries(parentStats),
            byPriority: priorityStats,
            byDueDate: dueDateStats
        });
    } catch (error) {
        console.error('Error fetching task statistics:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'タスク統計の取得に失敗しました'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__edf555ae._.js.map