{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/TodoCalender/task-finance-tracker/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Disclosure } from '@headlessui/react';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\n\nconst navigation = [\n  { name: 'ダッシュボード', href: '/', current: false },\n  { name: 'タスク管理', href: '/tasks', current: false },\n  { name: '収支管理', href: '/finance', current: false },\n];\n\nfunction classNames(...classes: string[]) {\n  return classes.filter(Boolean).join(' ');\n}\n\nexport default function Header() {\n  const router = useRouter();\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\n\n  const handleLogout = async () => {\n    setIsLoggingOut(true);\n    try {\n      const response = await fetch('/api/auth/logout', {\n        method: 'POST',\n      });\n\n      if (response.ok) {\n        router.push('/login');\n        router.refresh();\n      } else {\n        console.error('Logout failed');\n      }\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setIsLoggingOut(false);\n    }\n  };\n\n  return (\n    <Disclosure as=\"nav\" className=\"bg-white shadow\">\n      {({ open }) => (\n        <>\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex h-16 justify-between\">\n              <div className=\"flex\">\n                <div className=\"flex flex-shrink-0 items-center\">\n                  <h1 className=\"text-xl font-bold text-gray-900\">\n                    Task & Finance Tracker\n                  </h1>\n                </div>\n                <div className=\"hidden sm:ml-6 sm:flex sm:space-x-8\">\n                  {navigation.map((item) => (\n                    <a\n                      key={item.name}\n                      href={item.href}\n                      className={classNames(\n                        item.current\n                          ? 'border-indigo-500 text-gray-900'\n                          : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',\n                        'inline-flex items-center border-b-2 px-1 pt-1 text-sm font-medium'\n                      )}\n                      aria-current={item.current ? 'page' : undefined}\n                    >\n                      {item.name}\n                    </a>\n                  ))}\n                </div>\n              </div>\n              <div className=\"hidden sm:ml-6 sm:flex sm:items-center\">\n                <button\n                  onClick={handleLogout}\n                  disabled={isLoggingOut}\n                  className=\"rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 disabled:opacity-50\"\n                >\n                  {isLoggingOut ? 'ログアウト中...' : 'ログアウト'}\n                </button>\n              </div>\n              <div className=\"-mr-2 flex items-center sm:hidden\">\n                <Disclosure.Button className=\"relative inline-flex items-center justify-center rounded-md bg-white p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-inset\">\n                  <span className=\"absolute -inset-0.5\" />\n                  <span className=\"sr-only\">メニューを開く</span>\n                  {open ? (\n                    <XMarkIcon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                  ) : (\n                    <Bars3Icon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                  )}\n                </Disclosure.Button>\n              </div>\n            </div>\n          </div>\n\n          <Disclosure.Panel className=\"sm:hidden\">\n            <div className=\"space-y-1 pb-3 pt-2\">\n              {navigation.map((item) => (\n                <Disclosure.Button\n                  key={item.name}\n                  as=\"a\"\n                  href={item.href}\n                  className={classNames(\n                    item.current\n                      ? 'border-indigo-500 bg-indigo-50 text-indigo-700'\n                      : 'border-transparent text-gray-600 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-800',\n                    'block border-l-4 py-2 pl-3 pr-4 text-base font-medium'\n                  )}\n                  aria-current={item.current ? 'page' : undefined}\n                >\n                  {item.name}\n                </Disclosure.Button>\n              ))}\n              <div className=\"border-t border-gray-200 pt-4\">\n                <Disclosure.Button\n                  as=\"button\"\n                  onClick={handleLogout}\n                  disabled={isLoggingOut}\n                  className=\"block w-full px-4 py-2 text-left text-base font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-800 disabled:opacity-50\"\n                >\n                  {isLoggingOut ? 'ログアウト中...' : 'ログアウト'}\n                </Disclosure.Button>\n              </div>\n            </div>\n          </Disclosure.Panel>\n        </>\n      )}\n    </Disclosure>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAOA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAW,MAAM;QAAK,SAAS;IAAM;IAC7C;QAAE,MAAM;QAAS,MAAM;QAAU,SAAS;IAAM;IAChD;QAAE,MAAM;QAAQ,MAAM;QAAY,SAAS;IAAM;CAClD;AAED,SAAS,WAAW,GAAG,OAAiB;IACtC,OAAO,QAAQ,MAAM,CAAC,SAAS,IAAI,CAAC;AACtC;AAEe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,eAAe;QACnB,gBAAgB;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO,IAAI,CAAC;gBACZ,OAAO,OAAO;YAChB,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC,uLAAA,CAAA,aAAU;QAAC,IAAG;QAAM,WAAU;kBAC5B,CAAC,EAAE,IAAI,EAAE,iBACR;;kCACE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;sDAIlD,8OAAC;4CAAI,WAAU;sDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;oDAEC,MAAM,KAAK,IAAI;oDACf,WAAW,WACT,KAAK,OAAO,GACR,oCACA,8EACJ;oDAEF,gBAAc,KAAK,OAAO,GAAG,SAAS;8DAErC,KAAK,IAAI;mDAVL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAetB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,eAAe,cAAc;;;;;;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,MAAM;wCAAC,WAAU;;0DAC3B,8OAAC;gDAAK,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;4CACzB,qBACC,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAgB,eAAY;;;;;qEAEjD,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAgB,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO3D,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;wBAAC,WAAU;kCAC1B,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,uLAAA,CAAA,aAAU,CAAC,MAAM;wCAEhB,IAAG;wCACH,MAAM,KAAK,IAAI;wCACf,WAAW,WACT,KAAK,OAAO,GACR,mDACA,+FACJ;wCAEF,gBAAc,KAAK,OAAO,GAAG,SAAS;kDAErC,KAAK,IAAI;uCAXL,KAAK,IAAI;;;;;8CAclB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,MAAM;wCAChB,IAAG;wCACH,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,eAAe,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShD", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/TodoCalender/task-finance-tracker/src/components/TaskList.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Task } from '@/lib/kv';\nimport { \n  ChevronRightIcon, \n  ChevronDownIcon,\n  PencilIcon,\n  TrashIcon,\n  CalendarIcon,\n  CurrencyYenIcon\n} from '@heroicons/react/24/outline';\n\ninterface TaskWithChildren extends Task {\n  children: TaskWithChildren[];\n}\n\ninterface TaskListProps {\n  tasks: TaskWithChildren[];\n  onEdit: (task: Task) => void;\n  onDelete: (taskId: string) => void;\n  onStatusChange: (taskId: string, status: string) => void;\n}\n\ninterface TaskItemProps {\n  task: TaskWithChildren;\n  level: number;\n  onEdit: (task: Task) => void;\n  onDelete: (taskId: string) => void;\n  onStatusChange: (taskId: string, status: string) => void;\n}\n\nconst statusLabels = {\n  not_started: '未着手',\n  in_progress: '進行中',\n  completed: '完了',\n};\n\nconst statusColors = {\n  not_started: 'bg-gray-100 text-gray-800',\n  in_progress: 'bg-blue-100 text-blue-800',\n  completed: 'bg-green-100 text-green-800',\n};\n\nconst priorityLabels = {\n  low: '低',\n  medium: '中',\n  high: '高',\n};\n\nconst priorityColors = {\n  low: 'bg-gray-100 text-gray-800',\n  medium: 'bg-yellow-100 text-yellow-800',\n  high: 'bg-red-100 text-red-800',\n};\n\nfunction TaskItem({ task, level, onEdit, onDelete, onStatusChange }: TaskItemProps) {\n  const [isExpanded, setIsExpanded] = useState(true);\n  const hasChildren = task.children && task.children.length > 0;\n\n  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    onStatusChange(task.id, e.target.value);\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('ja-JP');\n  };\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('ja-JP').format(amount);\n  };\n\n  return (\n    <div className=\"border-l-2 border-gray-200\">\n      <div \n        className={`bg-white border border-gray-200 rounded-lg p-4 mb-2 ${\n          level > 0 ? 'ml-6' : ''\n        }`}\n      >\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex-1\">\n            <div className=\"flex items-center mb-2\">\n              {hasChildren && (\n                <button\n                  onClick={() => setIsExpanded(!isExpanded)}\n                  className=\"mr-2 p-1 hover:bg-gray-100 rounded\"\n                >\n                  {isExpanded ? (\n                    <ChevronDownIcon className=\"h-4 w-4\" />\n                  ) : (\n                    <ChevronRightIcon className=\"h-4 w-4\" />\n                  )}\n                </button>\n              )}\n              <h3 className={`text-lg font-medium ${\n                task.status === 'completed' ? 'line-through text-gray-500' : 'text-gray-900'\n              }`}>\n                {task.title}\n              </h3>\n            </div>\n\n            {task.description && (\n              <p className=\"text-gray-600 mb-3\">{task.description}</p>\n            )}\n\n            <div className=\"flex flex-wrap items-center gap-2 mb-3\">\n              {/* ステータス */}\n              <select\n                value={task.status}\n                onChange={handleStatusChange}\n                className={`px-2 py-1 text-xs font-medium rounded-full border-0 cursor-pointer ${statusColors[task.status]}`}\n                style={{\n                  backgroundColor: task.status === 'not_started' ? '#f3f4f6' :\n                                   task.status === 'in_progress' ? '#dbeafe' : '#dcfce7',\n                  color: task.status === 'not_started' ? '#374151' :\n                         task.status === 'in_progress' ? '#1e40af' : '#166534'\n                }}\n              >\n                <option value=\"not_started\">未着手</option>\n                <option value=\"in_progress\">進行中</option>\n                <option value=\"completed\">完了</option>\n              </select>\n\n              {/* 優先度 */}\n              <span className={`px-2 py-1 text-xs font-medium rounded-full ${priorityColors[task.priority]}`}>\n                優先度: {priorityLabels[task.priority]}\n              </span>\n\n              {/* 目標日 */}\n              {task.targetDate && (\n                <span className=\"inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-gray-100 rounded-full\">\n                  <CalendarIcon className=\"h-3 w-3 mr-1\" />\n                  {formatDate(task.targetDate)}\n                </span>\n              )}\n\n              {/* 財務影響 */}\n              {task.financialImpact && (\n                <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${\n                  task.financialImpact.type === 'income' \n                    ? 'text-green-700 bg-green-100' \n                    : 'text-red-700 bg-red-100'\n                }`}>\n                  <CurrencyYenIcon className=\"h-3 w-3 mr-1\" />\n                  {task.financialImpact.type === 'income' ? '+' : '-'}\n                  {formatCurrency(task.financialImpact.amount)}\n                </span>\n              )}\n            </div>\n\n            {/* 子タスクの進捗 */}\n            {hasChildren && (\n              <div className=\"mb-2\">\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <span>\n                    子タスク: {task.children.filter(c => c.status === 'completed').length} / {task.children.length} 完了\n                  </span>\n                </div>\n                <div className=\"w-full bg-gray-200 rounded-full h-2 mt-1\">\n                  <div \n                    className=\"bg-blue-600 h-2 rounded-full\" \n                    style={{ \n                      width: `${task.children.length > 0 \n                        ? (task.children.filter(c => c.status === 'completed').length / task.children.length) * 100 \n                        : 0}%` \n                    }}\n                  ></div>\n                </div>\n              </div>\n            )}\n\n            <div className=\"text-xs text-gray-500\">\n              作成: {formatDate(task.createdAt)} | 更新: {formatDate(task.updatedAt)}\n            </div>\n          </div>\n\n          {/* アクション */}\n          <div className=\"flex items-center space-x-2 ml-4\">\n            <button\n              onClick={() => onEdit(task)}\n              className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded\"\n              title=\"編集\"\n            >\n              <PencilIcon className=\"h-4 w-4\" />\n            </button>\n            <button\n              onClick={() => onDelete(task.id)}\n              className=\"p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded\"\n              title=\"削除\"\n            >\n              <TrashIcon className=\"h-4 w-4\" />\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* 子タスク */}\n      {hasChildren && isExpanded && (\n        <div className=\"ml-4\">\n          {task.children.map((childTask) => (\n            <TaskItem\n              key={childTask.id}\n              task={childTask}\n              level={level + 1}\n              onEdit={onEdit}\n              onDelete={onDelete}\n              onStatusChange={onStatusChange}\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default function TaskList({ tasks, onEdit, onDelete, onStatusChange }: TaskListProps) {\n  if (tasks.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"text-gray-500 mb-4\">タスクがありません</div>\n        <p className=\"text-sm text-gray-400\">\n          「新しいタスク」ボタンをクリックしてタスクを作成してください\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {tasks.map((task) => (\n        <TaskItem\n          key={task.id}\n          task={task}\n          level={0}\n          onEdit={onEdit}\n          onDelete={onDelete}\n          onStatusChange={onStatusChange}\n        />\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;AAgCA,MAAM,eAAe;IACnB,aAAa;IACb,aAAa;IACb,WAAW;AACb;AAEA,MAAM,eAAe;IACnB,aAAa;IACb,aAAa;IACb,WAAW;AACb;AAEA,MAAM,iBAAiB;IACrB,KAAK;IACL,QAAQ;IACR,MAAM;AACR;AAEA,MAAM,iBAAiB;IACrB,KAAK;IACL,QAAQ;IACR,MAAM;AACR;AAEA,SAAS,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAiB;IAChF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;IAE5D,MAAM,qBAAqB,CAAC;QAC1B,eAAe,KAAK,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;IACxC;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC;IACjD;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;IAC/C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAW,CAAC,oDAAoD,EAC9D,QAAQ,IAAI,SAAS,IACrB;0BAEF,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCACZ,6BACC,8OAAC;4CACC,SAAS,IAAM,cAAc,CAAC;4CAC9B,WAAU;sDAET,2BACC,8OAAC,6NAAA,CAAA,kBAAe;gDAAC,WAAU;;;;;qEAE3B,8OAAC,+NAAA,CAAA,mBAAgB;gDAAC,WAAU;;;;;;;;;;;sDAIlC,8OAAC;4CAAG,WAAW,CAAC,oBAAoB,EAClC,KAAK,MAAM,KAAK,cAAc,+BAA+B,iBAC7D;sDACC,KAAK,KAAK;;;;;;;;;;;;gCAId,KAAK,WAAW,kBACf,8OAAC;oCAAE,WAAU;8CAAsB,KAAK,WAAW;;;;;;8CAGrD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CACC,OAAO,KAAK,MAAM;4CAClB,UAAU;4CACV,WAAW,CAAC,mEAAmE,EAAE,YAAY,CAAC,KAAK,MAAM,CAAC,EAAE;4CAC5G,OAAO;gDACL,iBAAiB,KAAK,MAAM,KAAK,gBAAgB,YAChC,KAAK,MAAM,KAAK,gBAAgB,YAAY;gDAC7D,OAAO,KAAK,MAAM,KAAK,gBAAgB,YAChC,KAAK,MAAM,KAAK,gBAAgB,YAAY;4CACrD;;8DAEA,8OAAC;oDAAO,OAAM;8DAAc;;;;;;8DAC5B,8OAAC;oDAAO,OAAM;8DAAc;;;;;;8DAC5B,8OAAC;oDAAO,OAAM;8DAAY;;;;;;;;;;;;sDAI5B,8OAAC;4CAAK,WAAW,CAAC,2CAA2C,EAAE,cAAc,CAAC,KAAK,QAAQ,CAAC,EAAE;;gDAAE;gDACxF,cAAc,CAAC,KAAK,QAAQ,CAAC;;;;;;;wCAIpC,KAAK,UAAU,kBACd,8OAAC;4CAAK,WAAU;;8DACd,8OAAC,uNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;gDACvB,WAAW,KAAK,UAAU;;;;;;;wCAK9B,KAAK,eAAe,kBACnB,8OAAC;4CAAK,WAAW,CAAC,oEAAoE,EACpF,KAAK,eAAe,CAAC,IAAI,KAAK,WAC1B,gCACA,2BACJ;;8DACA,8OAAC,6NAAA,CAAA,kBAAe;oDAAC,WAAU;;;;;;gDAC1B,KAAK,eAAe,CAAC,IAAI,KAAK,WAAW,MAAM;gDAC/C,eAAe,KAAK,eAAe,CAAC,MAAM;;;;;;;;;;;;;gCAMhD,6BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;oDAAK;oDACG,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;oDAAC;oDAAI,KAAK,QAAQ,CAAC,MAAM;oDAAC;;;;;;;;;;;;sDAG/F,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,WAAU;gDACV,OAAO;oDACL,OAAO,GAAG,KAAK,QAAQ,CAAC,MAAM,GAAG,IAC7B,AAAC,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM,GAAG,KAAK,QAAQ,CAAC,MAAM,GAAI,MACtF,EAAE,CAAC,CAAC;gDACV;;;;;;;;;;;;;;;;;8CAMR,8OAAC;oCAAI,WAAU;;wCAAwB;wCAChC,WAAW,KAAK,SAAS;wCAAE;wCAAQ,WAAW,KAAK,SAAS;;;;;;;;;;;;;sCAKrE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,OAAO;oCACtB,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC,mNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,8OAAC;oCACC,SAAS,IAAM,SAAS,KAAK,EAAE;oCAC/B,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO5B,eAAe,4BACd,8OAAC;gBAAI,WAAU;0BACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,0BAClB,8OAAC;wBAEC,MAAM;wBACN,OAAO,QAAQ;wBACf,QAAQ;wBACR,UAAU;wBACV,gBAAgB;uBALX,UAAU,EAAE;;;;;;;;;;;;;;;;AAY/B;AAEe,SAAS,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAiB;IACzF,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BAAqB;;;;;;8BACpC,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;IAK3C;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;gBAEC,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,UAAU;gBACV,gBAAgB;eALX,KAAK,EAAE;;;;;;;;;;AAUtB", "debugId": null}}, {"offset": {"line": 672, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/TodoCalender/task-finance-tracker/src/components/TaskForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Dialog } from '@headlessui/react';\nimport { XMarkIcon } from '@heroicons/react/24/outline';\nimport { Task } from '@/lib/kv';\n\ninterface TaskFormProps {\n  task?: Task | null;\n  onSave: () => void;\n  onCancel: () => void;\n}\n\ninterface FormData {\n  title: string;\n  description: string;\n  targetDate: string;\n  priority: 'low' | 'medium' | 'high';\n  parentId: string;\n  financialImpact: {\n    enabled: boolean;\n    type: 'income' | 'expense';\n    amount: string;\n    description: string;\n  };\n}\n\nexport default function TaskForm({ task, onSave, onCancel }: TaskFormProps) {\n  const [formData, setFormData] = useState<FormData>({\n    title: '',\n    description: '',\n    targetDate: '',\n    priority: 'medium',\n    parentId: '',\n    financialImpact: {\n      enabled: false,\n      type: 'expense',\n      amount: '',\n      description: '',\n    },\n  });\n  const [parentTasks, setParentTasks] = useState<Task[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  // 編集時の初期値設定\n  useEffect(() => {\n    if (task) {\n      setFormData({\n        title: task.title,\n        description: task.description || '',\n        targetDate: task.targetDate || '',\n        priority: task.priority,\n        parentId: task.parentId || '',\n        financialImpact: {\n          enabled: !!task.financialImpact,\n          type: task.financialImpact?.type || 'expense',\n          amount: task.financialImpact?.amount?.toString() || '',\n          description: task.financialImpact?.description || '',\n        },\n      });\n    }\n  }, [task]);\n\n  // 親タスク候補を取得\n  useEffect(() => {\n    const fetchParentTasks = async () => {\n      try {\n        const response = await fetch('/api/tasks?parentId=');\n        if (response.ok) {\n          const data = await response.json();\n          // 編集中のタスクとその子タスクは除外\n          const filteredTasks = data.tasks.filter((t: Task) => \n            t.id !== task?.id && t.parentId !== task?.id\n          );\n          setParentTasks(filteredTasks);\n        }\n      } catch (error) {\n        console.error('Error fetching parent tasks:', error);\n      }\n    };\n\n    fetchParentTasks();\n  }, [task]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      // バリデーション\n      if (!formData.title.trim()) {\n        throw new Error('タイトルは必須です');\n      }\n\n      if (formData.financialImpact.enabled) {\n        if (!formData.financialImpact.amount || isNaN(Number(formData.financialImpact.amount))) {\n          throw new Error('財務影響の金額は数値で入力してください');\n        }\n        if (Number(formData.financialImpact.amount) <= 0) {\n          throw new Error('財務影響の金額は正の数値で入力してください');\n        }\n      }\n\n      // リクエストデータを構築\n      const requestData = {\n        title: formData.title.trim(),\n        description: formData.description.trim() || undefined,\n        targetDate: formData.targetDate || undefined,\n        priority: formData.priority,\n        parentId: formData.parentId || undefined,\n        financialImpact: formData.financialImpact.enabled ? {\n          type: formData.financialImpact.type,\n          amount: Number(formData.financialImpact.amount),\n          description: formData.financialImpact.description.trim() || undefined,\n        } : undefined,\n      };\n\n      // API呼び出し\n      const url = task ? `/api/tasks/${task.id}` : '/api/tasks';\n      const method = task ? 'PUT' : 'POST';\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(requestData),\n      });\n\n      if (!response.ok) {\n        const data = await response.json();\n        throw new Error(data.error || 'タスクの保存に失敗しました');\n      }\n\n      onSave();\n    } catch (error) {\n      console.error('Error saving task:', error);\n      setError(error instanceof Error ? error.message : 'タスクの保存に失敗しました');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleInputChange = (field: keyof FormData, value: any) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value,\n    }));\n  };\n\n  const handleFinancialImpactChange = (field: keyof FormData['financialImpact'], value: any) => {\n    setFormData(prev => ({\n      ...prev,\n      financialImpact: {\n        ...prev.financialImpact,\n        [field]: value,\n      },\n    }));\n  };\n\n  return (\n    <Dialog open={true} onClose={onCancel} className=\"relative z-50\">\n      <div className=\"fixed inset-0 bg-black/30\" aria-hidden=\"true\" />\n      \n      <div className=\"fixed inset-0 flex items-center justify-center p-4\">\n        <Dialog.Panel className=\"mx-auto max-w-2xl w-full bg-white rounded-lg shadow-xl\">\n          <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n            <Dialog.Title className=\"text-lg font-medium text-gray-900\">\n              {task ? 'タスクを編集' : '新しいタスクを作成'}\n            </Dialog.Title>\n            <button\n              onClick={onCancel}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              <XMarkIcon className=\"h-6 w-6\" />\n            </button>\n          </div>\n\n          <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n                <div className=\"text-sm text-red-700\">{error}</div>\n              </div>\n            )}\n\n            {/* タイトル */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                タイトル *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.title}\n                onChange={(e) => handleInputChange('title', e.target.value)}\n                className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n                placeholder=\"タスクのタイトルを入力\"\n                required\n              />\n            </div>\n\n            {/* 説明 */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                説明\n              </label>\n              <textarea\n                value={formData.description}\n                onChange={(e) => handleInputChange('description', e.target.value)}\n                rows={3}\n                className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n                placeholder=\"タスクの詳細説明（任意）\"\n              />\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* 目標日 */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  目標日\n                </label>\n                <input\n                  type=\"date\"\n                  value={formData.targetDate}\n                  onChange={(e) => handleInputChange('targetDate', e.target.value)}\n                  className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n                />\n              </div>\n\n              {/* 優先度 */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  優先度\n                </label>\n                <select\n                  value={formData.priority}\n                  onChange={(e) => handleInputChange('priority', e.target.value)}\n                  className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n                >\n                  <option value=\"low\">低</option>\n                  <option value=\"medium\">中</option>\n                  <option value=\"high\">高</option>\n                </select>\n              </div>\n            </div>\n\n            {/* 親タスク */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                親タスク\n              </label>\n              <select\n                value={formData.parentId}\n                onChange={(e) => handleInputChange('parentId', e.target.value)}\n                className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n              >\n                <option value=\"\">なし（ルートタスク）</option>\n                {parentTasks.map((parentTask) => (\n                  <option key={parentTask.id} value={parentTask.id}>\n                    {parentTask.title}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* 財務影響 */}\n            <div>\n              <div className=\"flex items-center mb-3\">\n                <input\n                  type=\"checkbox\"\n                  id=\"financialImpact\"\n                  checked={formData.financialImpact.enabled}\n                  onChange={(e) => handleFinancialImpactChange('enabled', e.target.checked)}\n                  className=\"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded\"\n                />\n                <label htmlFor=\"financialImpact\" className=\"ml-2 block text-sm font-medium text-gray-700\">\n                  財務影響あり\n                </label>\n              </div>\n\n              {formData.financialImpact.enabled && (\n                <div className=\"space-y-4 pl-6 border-l-2 border-gray-200\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        種類\n                      </label>\n                      <select\n                        value={formData.financialImpact.type}\n                        onChange={(e) => handleFinancialImpactChange('type', e.target.value)}\n                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n                      >\n                        <option value=\"income\">収入</option>\n                        <option value=\"expense\">支出</option>\n                      </select>\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        金額（円）\n                      </label>\n                      <input\n                        type=\"number\"\n                        value={formData.financialImpact.amount}\n                        onChange={(e) => handleFinancialImpactChange('amount', e.target.value)}\n                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n                        placeholder=\"0\"\n                        min=\"0\"\n                      />\n                    </div>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      財務影響の説明\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={formData.financialImpact.description}\n                      onChange={(e) => handleFinancialImpactChange('description', e.target.value)}\n                      className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n                      placeholder=\"財務影響の詳細\"\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* ボタン */}\n            <div className=\"flex justify-end space-x-3 pt-6 border-t border-gray-200\">\n              <button\n                type=\"button\"\n                onClick={onCancel}\n                className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\n              >\n                キャンセル\n              </button>\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {isLoading ? '保存中...' : (task ? '更新' : '作成')}\n              </button>\n            </div>\n          </form>\n        </Dialog.Panel>\n      </div>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AA2Be,SAAS,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAiB;IACxE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,OAAO;QACP,aAAa;QACb,YAAY;QACZ,UAAU;QACV,UAAU;QACV,iBAAiB;YACf,SAAS;YACT,MAAM;YACN,QAAQ;YACR,aAAa;QACf;IACF;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,YAAY;IACZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,YAAY;gBACV,OAAO,KAAK,KAAK;gBACjB,aAAa,KAAK,WAAW,IAAI;gBACjC,YAAY,KAAK,UAAU,IAAI;gBAC/B,UAAU,KAAK,QAAQ;gBACvB,UAAU,KAAK,QAAQ,IAAI;gBAC3B,iBAAiB;oBACf,SAAS,CAAC,CAAC,KAAK,eAAe;oBAC/B,MAAM,KAAK,eAAe,EAAE,QAAQ;oBACpC,QAAQ,KAAK,eAAe,EAAE,QAAQ,cAAc;oBACpD,aAAa,KAAK,eAAe,EAAE,eAAe;gBACpD;YACF;QACF;IACF,GAAG;QAAC;KAAK;IAET,YAAY;IACZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,oBAAoB;oBACpB,MAAM,gBAAgB,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,IACvC,EAAE,EAAE,KAAK,MAAM,MAAM,EAAE,QAAQ,KAAK,MAAM;oBAE5C,eAAe;gBACjB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;YAChD;QACF;QAEA;IACF,GAAG;QAAC;KAAK;IAET,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QAET,IAAI;YACF,UAAU;YACV,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;gBAC1B,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,SAAS,eAAe,CAAC,OAAO,EAAE;gBACpC,IAAI,CAAC,SAAS,eAAe,CAAC,MAAM,IAAI,MAAM,OAAO,SAAS,eAAe,CAAC,MAAM,IAAI;oBACtF,MAAM,IAAI,MAAM;gBAClB;gBACA,IAAI,OAAO,SAAS,eAAe,CAAC,MAAM,KAAK,GAAG;oBAChD,MAAM,IAAI,MAAM;gBAClB;YACF;YAEA,cAAc;YACd,MAAM,cAAc;gBAClB,OAAO,SAAS,KAAK,CAAC,IAAI;gBAC1B,aAAa,SAAS,WAAW,CAAC,IAAI,MAAM;gBAC5C,YAAY,SAAS,UAAU,IAAI;gBACnC,UAAU,SAAS,QAAQ;gBAC3B,UAAU,SAAS,QAAQ,IAAI;gBAC/B,iBAAiB,SAAS,eAAe,CAAC,OAAO,GAAG;oBAClD,MAAM,SAAS,eAAe,CAAC,IAAI;oBACnC,QAAQ,OAAO,SAAS,eAAe,CAAC,MAAM;oBAC9C,aAAa,SAAS,eAAe,CAAC,WAAW,CAAC,IAAI,MAAM;gBAC9D,IAAI;YACN;YAEA,UAAU;YACV,MAAM,MAAM,OAAO,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE,GAAG;YAC7C,MAAM,SAAS,OAAO,QAAQ;YAE9B,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAuB;QAChD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,8BAA8B,CAAC,OAA0C;QAC7E,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,iBAAiB;oBACf,GAAG,KAAK,eAAe;oBACvB,CAAC,MAAM,EAAE;gBACX;YACF,CAAC;IACH;IAEA,qBACE,8OAAC,+KAAA,CAAA,SAAM;QAAC,MAAM;QAAM,SAAS;QAAU,WAAU;;0BAC/C,8OAAC;gBAAI,WAAU;gBAA4B,eAAY;;;;;;0BAEvD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;oBAAC,WAAU;;sCACtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;oCAAC,WAAU;8CACrB,OAAO,WAAW;;;;;;8CAErB,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIzB,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;gCACrC,uBACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;8CAK3C,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;4CAC1D,WAAU;4CACV,aAAY;4CACZ,QAAQ;;;;;;;;;;;;8CAKZ,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,OAAO,SAAS,WAAW;4CAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;4CAChE,MAAM;4CACN,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,UAAU;oDAC1B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC/D,WAAU;;;;;;;;;;;;sDAKd,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,OAAO,SAAS,QAAQ;oDACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC7D,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAM;;;;;;sEACpB,8OAAC;4DAAO,OAAM;sEAAS;;;;;;sEACvB,8OAAC;4DAAO,OAAM;sEAAO;;;;;;;;;;;;;;;;;;;;;;;;8CAM3B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,OAAO,SAAS,QAAQ;4CACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC7D,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,YAAY,GAAG,CAAC,CAAC,2BAChB,8OAAC;wDAA2B,OAAO,WAAW,EAAE;kEAC7C,WAAW,KAAK;uDADN,WAAW,EAAE;;;;;;;;;;;;;;;;;8CAQhC,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,SAAS,SAAS,eAAe,CAAC,OAAO;oDACzC,UAAU,CAAC,IAAM,4BAA4B,WAAW,EAAE,MAAM,CAAC,OAAO;oDACxE,WAAU;;;;;;8DAEZ,8OAAC;oDAAM,SAAQ;oDAAkB,WAAU;8DAA+C;;;;;;;;;;;;wCAK3F,SAAS,eAAe,CAAC,OAAO,kBAC/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,8OAAC;oEACC,OAAO,SAAS,eAAe,CAAC,IAAI;oEACpC,UAAU,CAAC,IAAM,4BAA4B,QAAQ,EAAE,MAAM,CAAC,KAAK;oEACnE,WAAU;;sFAEV,8OAAC;4EAAO,OAAM;sFAAS;;;;;;sFACvB,8OAAC;4EAAO,OAAM;sFAAU;;;;;;;;;;;;;;;;;;sEAG5B,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,8OAAC;oEACC,MAAK;oEACL,OAAO,SAAS,eAAe,CAAC,MAAM;oEACtC,UAAU,CAAC,IAAM,4BAA4B,UAAU,EAAE,MAAM,CAAC,KAAK;oEACrE,WAAU;oEACV,aAAY;oEACZ,KAAI;;;;;;;;;;;;;;;;;;8DAIV,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,eAAe,CAAC,WAAW;4DAC3C,UAAU,CAAC,IAAM,4BAA4B,eAAe,EAAE,MAAM,CAAC,KAAK;4DAC1E,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;8CAQtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,YAAY,WAAY,OAAO,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD", "debugId": null}}, {"offset": {"line": 1272, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/TodoCalender/task-finance-tracker/src/app/tasks/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Header from '@/components/Header';\nimport TaskList from '@/components/TaskList';\nimport TaskForm from '@/components/TaskForm';\nimport { Task } from '@/lib/kv';\nimport { PlusIcon } from '@heroicons/react/24/outline';\n\ninterface TaskWithChildren extends Task {\n  children: TaskWithChildren[];\n}\n\nexport default function TasksPage() {\n  const [tasks, setTasks] = useState<TaskWithChildren[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [showForm, setShowForm] = useState(false);\n  const [editingTask, setEditingTask] = useState<Task | null>(null);\n  const [filter, setFilter] = useState<{\n    status?: string;\n    priority?: string;\n  }>({});\n\n  // タスク一覧を取得\n  const fetchTasks = async () => {\n    try {\n      setIsLoading(true);\n      const response = await fetch('/api/tasks/hierarchy');\n      if (!response.ok) {\n        throw new Error('タスクの取得に失敗しました');\n      }\n      const data = await response.json();\n      setTasks(data.tasks);\n      setError(null);\n    } catch (error) {\n      console.error('Error fetching tasks:', error);\n      setError('タスクの取得に失敗しました');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchTasks();\n  }, []);\n\n  // タスク作成・更新後のコールバック\n  const handleTaskSaved = () => {\n    setShowForm(false);\n    setEditingTask(null);\n    fetchTasks();\n  };\n\n  // タスク削除\n  const handleDeleteTask = async (taskId: string) => {\n    if (!confirm('このタスクを削除しますか？')) {\n      return;\n    }\n\n    try {\n      const response = await fetch(`/api/tasks/${taskId}`, {\n        method: 'DELETE',\n      });\n\n      if (!response.ok) {\n        const data = await response.json();\n        throw new Error(data.error || 'タスクの削除に失敗しました');\n      }\n\n      fetchTasks();\n    } catch (error) {\n      console.error('Error deleting task:', error);\n      alert(error instanceof Error ? error.message : 'タスクの削除に失敗しました');\n    }\n  };\n\n  // タスクステータス更新\n  const handleStatusChange = async (taskId: string, status: string) => {\n    try {\n      const response = await fetch(`/api/tasks/${taskId}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ status }),\n      });\n\n      if (!response.ok) {\n        throw new Error('ステータスの更新に失敗しました');\n      }\n\n      fetchTasks();\n    } catch (error) {\n      console.error('Error updating status:', error);\n      alert('ステータスの更新に失敗しました');\n    }\n  };\n\n  // フィルタリング\n  const filteredTasks = tasks.filter(task => {\n    if (filter.status && task.status !== filter.status) return false;\n    if (filter.priority && task.priority !== filter.priority) return false;\n    return true;\n  });\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      <main className=\"mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8\">\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">タスク管理</h1>\n              <p className=\"mt-2 text-gray-600\">\n                退職・移住計画のタスク進捗管理\n              </p>\n            </div>\n            <button\n              onClick={() => setShowForm(true)}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\n            >\n              <PlusIcon className=\"h-5 w-5 mr-2\" />\n              新しいタスク\n            </button>\n          </div>\n        </div>\n\n        {/* フィルター */}\n        <div className=\"mb-6 bg-white p-4 rounded-lg shadow\">\n          <div className=\"flex flex-wrap gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                ステータス\n              </label>\n              <select\n                value={filter.status || ''}\n                onChange={(e) => setFilter({ ...filter, status: e.target.value || undefined })}\n                className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n              >\n                <option value=\"\">すべて</option>\n                <option value=\"not_started\">未着手</option>\n                <option value=\"in_progress\">進行中</option>\n                <option value=\"completed\">完了</option>\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                優先度\n              </label>\n              <select\n                value={filter.priority || ''}\n                onChange={(e) => setFilter({ ...filter, priority: e.target.value || undefined })}\n                className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n              >\n                <option value=\"\">すべて</option>\n                <option value=\"high\">高</option>\n                <option value=\"medium\">中</option>\n                <option value=\"low\">低</option>\n              </select>\n            </div>\n            <div className=\"flex items-end\">\n              <button\n                onClick={() => setFilter({})}\n                className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\n              >\n                フィルターをクリア\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* エラー表示 */}\n        {error && (\n          <div className=\"mb-6 bg-red-50 border border-red-200 rounded-md p-4\">\n            <div className=\"text-sm text-red-700\">{error}</div>\n          </div>\n        )}\n\n        {/* ローディング表示 */}\n        {isLoading ? (\n          <div className=\"text-center py-12\">\n            <div className=\"text-gray-500\">読み込み中...</div>\n          </div>\n        ) : (\n          /* タスク一覧 */\n          <TaskList\n            tasks={filteredTasks}\n            onEdit={setEditingTask}\n            onDelete={handleDeleteTask}\n            onStatusChange={handleStatusChange}\n          />\n        )}\n\n        {/* タスクフォーム */}\n        {(showForm || editingTask) && (\n          <TaskForm\n            task={editingTask}\n            onSave={handleTaskSaved}\n            onCancel={() => {\n              setShowForm(false);\n              setEditingTask(null);\n            }}\n          />\n        )}\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AAae,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAGhC,CAAC;IAEJ,WAAW;IACX,MAAM,aAAa;QACjB,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YACA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,SAAS,KAAK,KAAK;YACnB,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,mBAAmB;IACnB,MAAM,kBAAkB;QACtB,YAAY;QACZ,eAAe;QACf;IACF;IAEA,QAAQ;IACR,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,QAAQ,kBAAkB;YAC7B;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,QAAQ,EAAE;gBACnD,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACjD;IACF;IAEA,aAAa;IACb,MAAM,qBAAqB,OAAO,QAAgB;QAChD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,QAAQ,EAAE;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAO;YAChC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,UAAU;IACV,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,IAAI,OAAO,MAAM,IAAI,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE,OAAO;QAC3D,IAAI,OAAO,QAAQ,IAAI,KAAK,QAAQ,KAAK,OAAO,QAAQ,EAAE,OAAO;QACjE,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAIpC,8OAAC;oCACC,SAAS,IAAM,YAAY;oCAC3B,WAAU;;sDAEV,8OAAC,+MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;kCAO3C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,OAAO,OAAO,MAAM,IAAI;4CACxB,UAAU,CAAC,IAAM,UAAU;oDAAE,GAAG,MAAM;oDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK,IAAI;gDAAU;4CAC5E,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,8OAAC;oDAAO,OAAM;8DAAc;;;;;;8DAC5B,8OAAC;oDAAO,OAAM;8DAAc;;;;;;8DAC5B,8OAAC;oDAAO,OAAM;8DAAY;;;;;;;;;;;;;;;;;;8CAG9B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,OAAO,OAAO,QAAQ,IAAI;4CAC1B,UAAU,CAAC,IAAM,UAAU;oDAAE,GAAG,MAAM;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK,IAAI;gDAAU;4CAC9E,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,8OAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAM;;;;;;;;;;;;;;;;;;8CAGxB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS,IAAM,UAAU,CAAC;wCAC1B,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;oBAQN,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCAAwB;;;;;;;;;;;oBAK1C,0BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;;;;;+BAGjC,SAAS,iBACT,8OAAC,8HAAA,CAAA,UAAQ;wBACP,OAAO;wBACP,QAAQ;wBACR,UAAU;wBACV,gBAAgB;;;;;;oBAKnB,CAAC,YAAY,WAAW,mBACvB,8OAAC,8HAAA,CAAA,UAAQ;wBACP,MAAM;wBACN,QAAQ;wBACR,UAAU;4BACR,YAAY;4BACZ,eAAe;wBACjB;;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}]}