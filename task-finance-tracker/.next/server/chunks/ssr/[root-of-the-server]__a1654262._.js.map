{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/TodoCalender/task-finance-tracker/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Disclosure } from '@headlessui/react';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\n\nconst navigation = [\n  { name: 'ダッシュボード', href: '/', current: false },\n  { name: 'タスク管理', href: '/tasks', current: false },\n  { name: '収支管理', href: '/finance', current: false },\n];\n\nfunction classNames(...classes: string[]) {\n  return classes.filter(Boolean).join(' ');\n}\n\nexport default function Header() {\n  const router = useRouter();\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\n\n  const handleLogout = async () => {\n    setIsLoggingOut(true);\n    try {\n      const response = await fetch('/api/auth/logout', {\n        method: 'POST',\n      });\n\n      if (response.ok) {\n        router.push('/login');\n        router.refresh();\n      } else {\n        console.error('Logout failed');\n      }\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setIsLoggingOut(false);\n    }\n  };\n\n  return (\n    <Disclosure as=\"nav\" className=\"navbar\">\n      {({ open }) => (\n        <>\n          <div className=\"container\">\n            <div className=\"flex justify-between items-center\">\n              <div className=\"flex items-center\">\n                <h1 className=\"text-gray-900\" style={{ fontSize: '1.25rem', fontWeight: '700' }}>\n                  Task & Finance Tracker\n                </h1>\n                <div className=\"hidden sm:block\" style={{ marginLeft: '2rem' }}>\n                  <div className=\"flex space-x-4\">\n                    {navigation.map((item) => (\n                      <a\n                        key={item.name}\n                        href={item.href}\n                        className={`nav-link ${item.current ? 'active' : ''}`}\n                      >\n                        {item.name}\n                      </a>\n                    ))}\n                  </div>\n                </div>\n              </div>\n              <div className=\"hidden sm:block\">\n                <button\n                  onClick={handleLogout}\n                  disabled={isLoggingOut}\n                  className=\"btn btn-secondary btn-sm\"\n                >\n                  {isLoggingOut ? 'ログアウト中...' : 'ログアウト'}\n                </button>\n              </div>\n              <div className=\"-mr-2 flex items-center sm:hidden\">\n                <Disclosure.Button className=\"relative inline-flex items-center justify-center rounded-md bg-white p-2 text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-inset transition-colors\">\n                  <span className=\"absolute -inset-0.5\" />\n                  <span className=\"sr-only\">メニューを開く</span>\n                  {open ? (\n                    <XMarkIcon className=\"block h-6 w-6 text-gray-700\" aria-hidden=\"true\" />\n                  ) : (\n                    <Bars3Icon className=\"block h-6 w-6 text-gray-700\" aria-hidden=\"true\" />\n                  )}\n                </Disclosure.Button>\n              </div>\n            </div>\n          </div>\n\n          <Disclosure.Panel className=\"sm:hidden\">\n            <div className=\"space-y-1 pb-3 pt-2\">\n              {navigation.map((item) => (\n                <Disclosure.Button\n                  key={item.name}\n                  as=\"a\"\n                  href={item.href}\n                  className={classNames(\n                    item.current\n                      ? 'border-indigo-500 bg-indigo-50 text-indigo-700'\n                      : 'border-transparent text-gray-700 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-900',\n                    'block border-l-4 py-2 pl-3 pr-4 text-base font-medium'\n                  )}\n                  aria-current={item.current ? 'page' : undefined}\n                >\n                  {item.name}\n                </Disclosure.Button>\n              ))}\n              <div className=\"border-t border-gray-200 pt-4\">\n                <Disclosure.Button\n                  as=\"button\"\n                  onClick={handleLogout}\n                  disabled={isLoggingOut}\n                  className=\"block w-full px-4 py-2 text-left text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 disabled:opacity-50\"\n                >\n                  {isLoggingOut ? 'ログアウト中...' : 'ログアウト'}\n                </Disclosure.Button>\n              </div>\n            </div>\n          </Disclosure.Panel>\n        </>\n      )}\n    </Disclosure>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAOA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAW,MAAM;QAAK,SAAS;IAAM;IAC7C;QAAE,MAAM;QAAS,MAAM;QAAU,SAAS;IAAM;IAChD;QAAE,MAAM;QAAQ,MAAM;QAAY,SAAS;IAAM;CAClD;AAED,SAAS,WAAW,GAAG,OAAiB;IACtC,OAAO,QAAQ,MAAM,CAAC,SAAS,IAAI,CAAC;AACtC;AAEe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,eAAe;QACnB,gBAAgB;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO,IAAI,CAAC;gBACZ,OAAO,OAAO;YAChB,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC,uLAAA,CAAA,aAAU;QAAC,IAAG;QAAM,WAAU;kBAC5B,CAAC,EAAE,IAAI,EAAE,iBACR;;kCACE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;4CAAgB,OAAO;gDAAE,UAAU;gDAAW,YAAY;4CAAM;sDAAG;;;;;;sDAGjF,8OAAC;4CAAI,WAAU;4CAAkB,OAAO;gDAAE,YAAY;4CAAO;sDAC3D,cAAA,8OAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;wDAEC,MAAM,KAAK,IAAI;wDACf,WAAW,CAAC,SAAS,EAAE,KAAK,OAAO,GAAG,WAAW,IAAI;kEAEpD,KAAK,IAAI;uDAJL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;8CAUxB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,eAAe,cAAc;;;;;;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,MAAM;wCAAC,WAAU;;0DAC3B,8OAAC;gDAAK,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;4CACzB,qBACC,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;gDAA8B,eAAY;;;;;qEAE/D,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;gDAA8B,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOzE,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;wBAAC,WAAU;kCAC1B,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,uLAAA,CAAA,aAAU,CAAC,MAAM;wCAEhB,IAAG;wCACH,MAAM,KAAK,IAAI;wCACf,WAAW,WACT,KAAK,OAAO,GACR,mDACA,+FACJ;wCAEF,gBAAc,KAAK,OAAO,GAAG,SAAS;kDAErC,KAAK,IAAI;uCAXL,KAAK,IAAI;;;;;8CAclB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,MAAM;wCAChB,IAAG;wCACH,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,eAAe,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShD", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/TodoCalender/task-finance-tracker/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Header from '@/components/Header';\n\ninterface TaskStats {\n  overall: {\n    total: number;\n    completed: number;\n    inProgress: number;\n    notStarted: number;\n    completionRate: number;\n  };\n  byParent: Record<string, {\n    total: number;\n    completed: number;\n    inProgress: number;\n    notStarted: number;\n    title: string;\n  }>;\n}\n\nexport default function Home() {\n  const [stats, setStats] = useState<TaskStats | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchStats = async () => {\n      try {\n        const response = await fetch('/api/tasks/hierarchy', {\n          method: 'POST',\n        });\n        if (response.ok) {\n          const data = await response.json();\n          setStats(data);\n        }\n      } catch (error) {\n        console.error('Error fetching stats:', error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchStats();\n  }, []);\n  return (\n    <div className=\"min-h-screen\">\n      <Header />\n      <main className=\"container py-8\">\n        <div className=\"mb-8 fade-in\">\n          <h1>ダッシュボード</h1>\n          <p>退職・移住計画の進捗状況と収支管理の概要</p>\n        </div>\n\n        <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-2 slide-in\">\n          {/* タスク進捗サマリー */}\n          <div className=\"card\">\n            <h2>タスク進捗サマリー</h2>\n              {isLoading ? (\n                <div className=\"text-center py-4 text-gray-700\">読み込み中...</div>\n              ) : stats ? (\n                <div className=\"space-y-4\">\n                  {/* 全体進捗 */}\n                  <div className=\"mb-6\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <span className=\"text-sm font-medium text-gray-900\">全体進捗</span>\n                      <span className=\"text-sm font-medium text-gray-900\">\n                        {stats.overall.completed}/{stats.overall.total} 完了 ({stats.overall.completionRate}%)\n                      </span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-3\">\n                      <div\n                        className=\"bg-blue-600 h-3 rounded-full transition-all duration-300\"\n                        style={{ width: `${stats.overall.completionRate}%` }}\n                      ></div>\n                    </div>\n                    <div className=\"flex justify-between text-xs text-gray-700 mt-1\">\n                      <span>未着手: {stats.overall.notStarted}</span>\n                      <span>進行中: {stats.overall.inProgress}</span>\n                      <span>完了: {stats.overall.completed}</span>\n                    </div>\n                  </div>\n\n                  {/* 親タスク別進捗 */}\n                  {Object.entries(stats.byParent).map(([parentId, parentStats]) => (\n                    <div key={parentId}>\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-sm text-gray-700 truncate\" title={parentStats.title}>\n                          {parentStats.title.length > 20\n                            ? `${parentStats.title.substring(0, 20)}...`\n                            : parentStats.title}\n                        </span>\n                        <span className=\"text-sm font-medium text-gray-900\">\n                          {parentStats.completed}/{parentStats.total} 完了\n                        </span>\n                      </div>\n                      <div className=\"w-full bg-gray-200 rounded-full h-2 mt-1\">\n                        <div\n                          className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                          style={{\n                            width: `${parentStats.total > 0\n                              ? (parentStats.completed / parentStats.total) * 100\n                              : 0}%`\n                          }}\n                        ></div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <div className=\"text-center py-4 text-gray-700\">データを取得できませんでした</div>\n              )}\n          </div>\n\n          {/* 収支サマリー */}\n          <div className=\"card\">\n            <h2>収支サマリー</h2>\n            <div className=\"space-y-4\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">年収目標</span>\n                <span style={{ color: '#48bb78', fontWeight: '600' }}>800-1000万円</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">月間生活費</span>\n                <span style={{ color: '#f56565', fontWeight: '600' }}>30万円</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">現在資産</span>\n                <span style={{ color: '#667eea', fontWeight: '600' }}>2500万円</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">予想余剰分</span>\n                <span style={{ color: '#48bb78', fontWeight: '600' }}>200-340万円/年</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* クイックアクション */}\n        <div className=\"mt-8 fade-in\">\n          <h2>クイックアクション</h2>\n          <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-3\">\n            <a href=\"/tasks\" className=\"card\" style={{ textDecoration: 'none', color: 'inherit' }}>\n              <h3>タスク管理</h3>\n              <p>進捗状況の確認と更新</p>\n            </a>\n            <a href=\"/finance\" className=\"card\" style={{ textDecoration: 'none', color: 'inherit' }}>\n              <h3>収支管理</h3>\n              <p>収支計画の確認と更新</p>\n            </a>\n            <button\n              className=\"card\"\n              onClick={() => window.location.reload()}\n              style={{ textAlign: 'left', border: 'none', background: 'transparent' }}\n            >\n              <h3>データ更新</h3>\n              <p>最新情報を取得</p>\n            </button>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAsBe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa;YACjB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,wBAAwB;oBACnD,QAAQ;gBACV;gBACA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,SAAS;gBACX;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;YACzC,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IACL,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;;;;;;;kCAGL,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAG;;;;;;oCACD,0BACC,8OAAC;wCAAI,WAAU;kDAAiC;;;;;+CAC9C,sBACF,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAoC;;;;;;0EACpD,8OAAC;gEAAK,WAAU;;oEACb,MAAM,OAAO,CAAC,SAAS;oEAAC;oEAAE,MAAM,OAAO,CAAC,KAAK;oEAAC;oEAAM,MAAM,OAAO,CAAC,cAAc;oEAAC;;;;;;;;;;;;;kEAGtF,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,OAAO,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC;4DAAC;;;;;;;;;;;kEAGvD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;oEAAK;oEAAM,MAAM,OAAO,CAAC,UAAU;;;;;;;0EACpC,8OAAC;;oEAAK;oEAAM,MAAM,OAAO,CAAC,UAAU;;;;;;;0EACpC,8OAAC;;oEAAK;oEAAK,MAAM,OAAO,CAAC,SAAS;;;;;;;;;;;;;;;;;;;4CAKrC,OAAO,OAAO,CAAC,MAAM,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU,YAAY,iBAC1D,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;oEAAiC,OAAO,YAAY,KAAK;8EACtE,YAAY,KAAK,CAAC,MAAM,GAAG,KACxB,GAAG,YAAY,KAAK,CAAC,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAC1C,YAAY,KAAK;;;;;;8EAEvB,8OAAC;oEAAK,WAAU;;wEACb,YAAY,SAAS;wEAAC;wEAAE,YAAY,KAAK;wEAAC;;;;;;;;;;;;;sEAG/C,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO,GAAG,YAAY,KAAK,GAAG,IAC1B,AAAC,YAAY,SAAS,GAAG,YAAY,KAAK,GAAI,MAC9C,EAAE,CAAC,CAAC;gEACV;;;;;;;;;;;;mDAlBI;;;;;;;;;;6DAyBd,8OAAC;wCAAI,WAAU;kDAAiC;;;;;;;;;;;;0CAKtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,OAAO;4DAAE,OAAO;4DAAW,YAAY;wDAAM;kEAAG;;;;;;;;;;;;0DAExD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,OAAO;4DAAE,OAAO;4DAAW,YAAY;wDAAM;kEAAG;;;;;;;;;;;;0DAExD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,OAAO;4DAAE,OAAO;4DAAW,YAAY;wDAAM;kEAAG;;;;;;;;;;;;0DAExD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,OAAO;4DAAE,OAAO;4DAAW,YAAY;wDAAM;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO9D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAS,WAAU;wCAAO,OAAO;4CAAE,gBAAgB;4CAAQ,OAAO;wCAAU;;0DAClF,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAE;;;;;;;;;;;;kDAEL,8OAAC;wCAAE,MAAK;wCAAW,WAAU;wCAAO,OAAO;4CAAE,gBAAgB;4CAAQ,OAAO;wCAAU;;0DACpF,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAE;;;;;;;;;;;;kDAEL,8OAAC;wCACC,WAAU;wCACV,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wCACrC,OAAO;4CAAE,WAAW;4CAAQ,QAAQ;4CAAQ,YAAY;wCAAc;;0DAEtE,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}]}