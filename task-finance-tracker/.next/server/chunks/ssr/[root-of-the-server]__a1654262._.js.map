{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/TodoCalender/task-finance-tracker/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Disclosure } from '@headlessui/react';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\n\nconst navigation = [\n  { name: 'ダッシュボード', href: '/', current: false },\n  { name: 'タスク管理', href: '/tasks', current: false },\n  { name: '収支管理', href: '/finance', current: false },\n];\n\nfunction classNames(...classes: string[]) {\n  return classes.filter(Boolean).join(' ');\n}\n\nexport default function Header() {\n  const router = useRouter();\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\n\n  const handleLogout = async () => {\n    setIsLoggingOut(true);\n    try {\n      const response = await fetch('/api/auth/logout', {\n        method: 'POST',\n      });\n\n      if (response.ok) {\n        router.push('/login');\n        router.refresh();\n      } else {\n        console.error('Logout failed');\n      }\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setIsLoggingOut(false);\n    }\n  };\n\n  return (\n    <Disclosure as=\"nav\" className=\"bg-white shadow\">\n      {({ open }) => (\n        <>\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex h-16 justify-between\">\n              <div className=\"flex\">\n                <div className=\"flex flex-shrink-0 items-center\">\n                  <h1 className=\"text-xl font-bold text-gray-900\">\n                    Task & Finance Tracker\n                  </h1>\n                </div>\n                <div className=\"hidden sm:ml-6 sm:flex sm:space-x-8\">\n                  {navigation.map((item) => (\n                    <a\n                      key={item.name}\n                      href={item.href}\n                      className={classNames(\n                        item.current\n                          ? 'border-indigo-500 text-gray-900'\n                          : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',\n                        'inline-flex items-center border-b-2 px-1 pt-1 text-sm font-medium'\n                      )}\n                      aria-current={item.current ? 'page' : undefined}\n                    >\n                      {item.name}\n                    </a>\n                  ))}\n                </div>\n              </div>\n              <div className=\"hidden sm:ml-6 sm:flex sm:items-center\">\n                <button\n                  onClick={handleLogout}\n                  disabled={isLoggingOut}\n                  className=\"rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 disabled:opacity-50\"\n                >\n                  {isLoggingOut ? 'ログアウト中...' : 'ログアウト'}\n                </button>\n              </div>\n              <div className=\"-mr-2 flex items-center sm:hidden\">\n                <Disclosure.Button className=\"relative inline-flex items-center justify-center rounded-md bg-white p-2 text-gray-600 hover:bg-gray-100 hover:text-gray-800 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-inset transition-colors\">\n                  <span className=\"absolute -inset-0.5\" />\n                  <span className=\"sr-only\">メニューを開く</span>\n                  {open ? (\n                    <XMarkIcon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                  ) : (\n                    <Bars3Icon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                  )}\n                </Disclosure.Button>\n              </div>\n            </div>\n          </div>\n\n          <Disclosure.Panel className=\"sm:hidden\">\n            <div className=\"space-y-1 pb-3 pt-2\">\n              {navigation.map((item) => (\n                <Disclosure.Button\n                  key={item.name}\n                  as=\"a\"\n                  href={item.href}\n                  className={classNames(\n                    item.current\n                      ? 'border-indigo-500 bg-indigo-50 text-indigo-700'\n                      : 'border-transparent text-gray-600 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-800',\n                    'block border-l-4 py-2 pl-3 pr-4 text-base font-medium'\n                  )}\n                  aria-current={item.current ? 'page' : undefined}\n                >\n                  {item.name}\n                </Disclosure.Button>\n              ))}\n              <div className=\"border-t border-gray-200 pt-4\">\n                <Disclosure.Button\n                  as=\"button\"\n                  onClick={handleLogout}\n                  disabled={isLoggingOut}\n                  className=\"block w-full px-4 py-2 text-left text-base font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-800 disabled:opacity-50\"\n                >\n                  {isLoggingOut ? 'ログアウト中...' : 'ログアウト'}\n                </Disclosure.Button>\n              </div>\n            </div>\n          </Disclosure.Panel>\n        </>\n      )}\n    </Disclosure>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAOA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAW,MAAM;QAAK,SAAS;IAAM;IAC7C;QAAE,MAAM;QAAS,MAAM;QAAU,SAAS;IAAM;IAChD;QAAE,MAAM;QAAQ,MAAM;QAAY,SAAS;IAAM;CAClD;AAED,SAAS,WAAW,GAAG,OAAiB;IACtC,OAAO,QAAQ,MAAM,CAAC,SAAS,IAAI,CAAC;AACtC;AAEe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,eAAe;QACnB,gBAAgB;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO,IAAI,CAAC;gBACZ,OAAO,OAAO;YAChB,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC,uLAAA,CAAA,aAAU;QAAC,IAAG;QAAM,WAAU;kBAC5B,CAAC,EAAE,IAAI,EAAE,iBACR;;kCACE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;sDAIlD,8OAAC;4CAAI,WAAU;sDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;oDAEC,MAAM,KAAK,IAAI;oDACf,WAAW,WACT,KAAK,OAAO,GACR,oCACA,8EACJ;oDAEF,gBAAc,KAAK,OAAO,GAAG,SAAS;8DAErC,KAAK,IAAI;mDAVL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAetB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,eAAe,cAAc;;;;;;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,MAAM;wCAAC,WAAU;;0DAC3B,8OAAC;gDAAK,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;4CACzB,qBACC,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAgB,eAAY;;;;;qEAEjD,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAgB,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO3D,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;wBAAC,WAAU;kCAC1B,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,uLAAA,CAAA,aAAU,CAAC,MAAM;wCAEhB,IAAG;wCACH,MAAM,KAAK,IAAI;wCACf,WAAW,WACT,KAAK,OAAO,GACR,mDACA,+FACJ;wCAEF,gBAAc,KAAK,OAAO,GAAG,SAAS;kDAErC,KAAK,IAAI;uCAXL,KAAK,IAAI;;;;;8CAclB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,MAAM;wCAChB,IAAG;wCACH,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,eAAe,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShD", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/TodoCalender/task-finance-tracker/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Header from '@/components/Header';\n\ninterface TaskStats {\n  overall: {\n    total: number;\n    completed: number;\n    inProgress: number;\n    notStarted: number;\n    completionRate: number;\n  };\n  byParent: Record<string, {\n    total: number;\n    completed: number;\n    inProgress: number;\n    notStarted: number;\n    title: string;\n  }>;\n}\n\nexport default function Home() {\n  const [stats, setStats] = useState<TaskStats | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchStats = async () => {\n      try {\n        const response = await fetch('/api/tasks/hierarchy', {\n          method: 'POST',\n        });\n        if (response.ok) {\n          const data = await response.json();\n          setStats(data);\n        }\n      } catch (error) {\n        console.error('Error fetching stats:', error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchStats();\n  }, []);\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      <main className=\"mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">ダッシュボード</h1>\n          <p className=\"mt-2 text-gray-600\">\n            退職・移住計画の進捗状況と収支管理の概要\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-2\">\n          {/* タスク進捗サマリー */}\n          <div className=\"bg-white overflow-hidden shadow rounded-lg border border-gray-200\">\n            <div className=\"p-6\">\n              <h2 className=\"text-lg font-medium text-gray-900 mb-4\">\n                タスク進捗サマリー\n              </h2>\n              {isLoading ? (\n                <div className=\"text-center py-4 text-gray-500\">読み込み中...</div>\n              ) : stats ? (\n                <div className=\"space-y-4\">\n                  {/* 全体進捗 */}\n                  <div className=\"mb-6\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <span className=\"text-sm font-medium text-gray-900\">全体進捗</span>\n                      <span className=\"text-sm font-medium text-gray-900\">\n                        {stats.overall.completed}/{stats.overall.total} 完了 ({stats.overall.completionRate}%)\n                      </span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-3\">\n                      <div\n                        className=\"bg-blue-600 h-3 rounded-full transition-all duration-300\"\n                        style={{ width: `${stats.overall.completionRate}%` }}\n                      ></div>\n                    </div>\n                    <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n                      <span>未着手: {stats.overall.notStarted}</span>\n                      <span>進行中: {stats.overall.inProgress}</span>\n                      <span>完了: {stats.overall.completed}</span>\n                    </div>\n                  </div>\n\n                  {/* 親タスク別進捗 */}\n                  {Object.entries(stats.byParent).map(([parentId, parentStats]) => (\n                    <div key={parentId}>\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-sm text-gray-600 truncate\" title={parentStats.title}>\n                          {parentStats.title.length > 20\n                            ? `${parentStats.title.substring(0, 20)}...`\n                            : parentStats.title}\n                        </span>\n                        <span className=\"text-sm font-medium text-gray-900\">\n                          {parentStats.completed}/{parentStats.total} 完了\n                        </span>\n                      </div>\n                      <div className=\"w-full bg-gray-200 rounded-full h-2 mt-1\">\n                        <div\n                          className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                          style={{\n                            width: `${parentStats.total > 0\n                              ? (parentStats.completed / parentStats.total) * 100\n                              : 0}%`\n                          }}\n                        ></div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <div className=\"text-center py-4 text-gray-500\">データを取得できませんでした</div>\n              )}\n            </div>\n          </div>\n\n          {/* 収支サマリー */}\n          <div className=\"bg-white overflow-hidden shadow rounded-lg border border-gray-200\">\n            <div className=\"p-6\">\n              <h2 className=\"text-lg font-medium text-gray-900 mb-4\">\n                収支サマリー\n              </h2>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600\">年収目標</span>\n                  <span className=\"text-sm font-medium text-green-600\">800-1000万円</span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600\">月間生活費</span>\n                  <span className=\"text-sm font-medium text-red-600\">30万円</span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600\">現在資産</span>\n                  <span className=\"text-sm font-medium text-blue-600\">2500万円</span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600\">予想余剰分</span>\n                  <span className=\"text-sm font-medium text-green-600\">200-340万円/年</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* クイックアクション */}\n        <div className=\"mt-8\">\n          <h2 className=\"text-lg font-medium text-gray-900 mb-4\">クイックアクション</h2>\n          <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-3\">\n            <a\n              href=\"/tasks\"\n              className=\"bg-white p-6 rounded-lg shadow hover:shadow-md transition-all border border-gray-200 hover:border-indigo-300\"\n            >\n              <h3 className=\"text-lg font-medium text-gray-900\">タスク管理</h3>\n              <p className=\"mt-2 text-sm text-gray-600\">\n                進捗状況の確認と更新\n              </p>\n            </a>\n            <a\n              href=\"/finance\"\n              className=\"bg-white p-6 rounded-lg shadow hover:shadow-md transition-all border border-gray-200 hover:border-indigo-300\"\n            >\n              <h3 className=\"text-lg font-medium text-gray-900\">収支管理</h3>\n              <p className=\"mt-2 text-sm text-gray-600\">\n                収支計画の確認と更新\n              </p>\n            </a>\n            <button\n              className=\"bg-white p-6 rounded-lg shadow hover:shadow-md transition-all text-left border border-gray-200 hover:border-indigo-300\"\n              onClick={() => window.location.reload()}\n            >\n              <h3 className=\"text-lg font-medium text-gray-900\">データ更新</h3>\n              <p className=\"mt-2 text-sm text-gray-600\">\n                最新情報を取得\n              </p>\n            </button>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAsBe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa;YACjB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,wBAAwB;oBACnD,QAAQ;gBACV;gBACA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,SAAS;gBACX;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;YACzC,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IACL,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAKpC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;wCAGtD,0BACC,8OAAC;4CAAI,WAAU;sDAAiC;;;;;mDAC9C,sBACF,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAoC;;;;;;8EACpD,8OAAC;oEAAK,WAAU;;wEACb,MAAM,OAAO,CAAC,SAAS;wEAAC;wEAAE,MAAM,OAAO,CAAC,KAAK;wEAAC;wEAAM,MAAM,OAAO,CAAC,cAAc;wEAAC;;;;;;;;;;;;;sEAGtF,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC;gEAAC;;;;;;;;;;;sEAGvD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;wEAAK;wEAAM,MAAM,OAAO,CAAC,UAAU;;;;;;;8EACpC,8OAAC;;wEAAK;wEAAM,MAAM,OAAO,CAAC,UAAU;;;;;;;8EACpC,8OAAC;;wEAAK;wEAAK,MAAM,OAAO,CAAC,SAAS;;;;;;;;;;;;;;;;;;;gDAKrC,OAAO,OAAO,CAAC,MAAM,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU,YAAY,iBAC1D,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;wEAAiC,OAAO,YAAY,KAAK;kFACtE,YAAY,KAAK,CAAC,MAAM,GAAG,KACxB,GAAG,YAAY,KAAK,CAAC,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAC1C,YAAY,KAAK;;;;;;kFAEvB,8OAAC;wEAAK,WAAU;;4EACb,YAAY,SAAS;4EAAC;4EAAE,YAAY,KAAK;4EAAC;;;;;;;;;;;;;0EAG/C,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,WAAU;oEACV,OAAO;wEACL,OAAO,GAAG,YAAY,KAAK,GAAG,IAC1B,AAAC,YAAY,SAAS,GAAG,YAAY,KAAK,GAAI,MAC9C,EAAE,CAAC,CAAC;oEACV;;;;;;;;;;;;uDAlBI;;;;;;;;;;iEAyBd,8OAAC;4CAAI,WAAU;sDAAiC;;;;;;;;;;;;;;;;;0CAMtD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDAGvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEAAqC;;;;;;;;;;;;8DAEvD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEAAmC;;;;;;;;;;;;8DAErD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEAAoC;;;;;;;;;;;;8DAEtD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,WAAU;;0DAEV,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;kDAI5C,8OAAC;wCACC,MAAK;wCACL,WAAU;;0DAEV,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;kDAI5C,8OAAC;wCACC,WAAU;wCACV,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;;0DAErC,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxD", "debugId": null}}]}