{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/TodoCalender/task-finance-tracker/src/lib/auth.ts"], "sourcesContent": ["import { cookies } from 'next/headers';\nimport { NextRequest, NextResponse } from 'next/server';\n\n// 認証設定\nconst AUTH_COOKIE_NAME = 'auth-session';\nconst SESSION_DURATION = 24 * 60 * 60 * 1000; // 24時間\n\n// 環境変数から認証情報を取得\nconst getAuthCredentials = () => {\n  const username = process.env.AUTH_USERNAME;\n  const password = process.env.AUTH_PASSWORD;\n  \n  if (!username || !password) {\n    throw new Error('AUTH_USERNAME and AUTH_PASSWORD must be set in environment variables');\n  }\n  \n  return { username, password };\n};\n\n// ログイン検証\nexport const validateLogin = (username: string, password: string): boolean => {\n  try {\n    const credentials = getAuthCredentials();\n    return username === credentials.username && password === credentials.password;\n  } catch (error) {\n    console.error('Auth validation error:', error);\n    return false;\n  }\n};\n\n// セッション作成\nexport const createSession = async (): Promise<void> => {\n  const cookieStore = await cookies();\n  const sessionData = {\n    authenticated: true,\n    timestamp: Date.now(),\n  };\n  \n  cookieStore.set(AUTH_COOKIE_NAME, JSON.stringify(sessionData), {\n    httpOnly: true,\n    secure: process.env.NODE_ENV === 'production',\n    sameSite: 'lax',\n    maxAge: SESSION_DURATION / 1000, // seconds\n  });\n};\n\n// セッション検証\nexport const validateSession = async (): Promise<boolean> => {\n  try {\n    const cookieStore = await cookies();\n    const sessionCookie = cookieStore.get(AUTH_COOKIE_NAME);\n    \n    if (!sessionCookie) {\n      return false;\n    }\n    \n    const sessionData = JSON.parse(sessionCookie.value);\n    const now = Date.now();\n    \n    // セッションの有効期限チェック\n    if (now - sessionData.timestamp > SESSION_DURATION) {\n      return false;\n    }\n    \n    return sessionData.authenticated === true;\n  } catch (error) {\n    console.error('Session validation error:', error);\n    return false;\n  }\n};\n\n// セッション削除\nexport const destroySession = async (): Promise<void> => {\n  const cookieStore = await cookies();\n  cookieStore.delete(AUTH_COOKIE_NAME);\n};\n\n// ミドルウェア用の認証チェック\nexport const checkAuthMiddleware = async (request: NextRequest): Promise<NextResponse | null> => {\n  const { pathname } = request.nextUrl;\n  \n  // ログインページとAPIルートは除外\n  if (pathname === '/login' || pathname.startsWith('/api/auth')) {\n    return null;\n  }\n  \n  try {\n    const sessionCookie = request.cookies.get(AUTH_COOKIE_NAME);\n    \n    if (!sessionCookie) {\n      return NextResponse.redirect(new URL('/login', request.url));\n    }\n    \n    const sessionData = JSON.parse(sessionCookie.value);\n    const now = Date.now();\n    \n    // セッションの有効期限チェック\n    if (now - sessionData.timestamp > SESSION_DURATION || !sessionData.authenticated) {\n      const response = NextResponse.redirect(new URL('/login', request.url));\n      response.cookies.delete(AUTH_COOKIE_NAME);\n      return response;\n    }\n    \n    return null; // 認証OK、処理続行\n  } catch (error) {\n    console.error('Middleware auth error:', error);\n    const response = NextResponse.redirect(new URL('/login', request.url));\n    response.cookies.delete(AUTH_COOKIE_NAME);\n    return response;\n  }\n};\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEA,OAAO;AACP,MAAM,mBAAmB;AACzB,MAAM,mBAAmB,KAAK,KAAK,KAAK,MAAM,OAAO;AAErD,gBAAgB;AAChB,MAAM,qBAAqB;IACzB,MAAM,WAAW,QAAQ,GAAG,CAAC,aAAa;IAC1C,MAAM,WAAW,QAAQ,GAAG,CAAC,aAAa;IAE1C,IAAI,CAAC,YAAY,CAAC,UAAU;QAC1B,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;QAAE;QAAU;IAAS;AAC9B;AAGO,MAAM,gBAAgB,CAAC,UAAkB;IAC9C,IAAI;QACF,MAAM,cAAc;QACpB,OAAO,aAAa,YAAY,QAAQ,IAAI,aAAa,YAAY,QAAQ;IAC/E,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;IACT;AACF;AAGO,MAAM,gBAAgB;IAC3B,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,cAAc;QAClB,eAAe;QACf,WAAW,KAAK,GAAG;IACrB;IAEA,YAAY,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC,cAAc;QAC7D,UAAU;QACV,QAAQ,oDAAyB;QACjC,UAAU;QACV,QAAQ,mBAAmB;IAC7B;AACF;AAGO,MAAM,kBAAkB;IAC7B,IAAI;QACF,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAChC,MAAM,gBAAgB,YAAY,GAAG,CAAC;QAEtC,IAAI,CAAC,eAAe;YAClB,OAAO;QACT;QAEA,MAAM,cAAc,KAAK,KAAK,CAAC,cAAc,KAAK;QAClD,MAAM,MAAM,KAAK,GAAG;QAEpB,iBAAiB;QACjB,IAAI,MAAM,YAAY,SAAS,GAAG,kBAAkB;YAClD,OAAO;QACT;QAEA,OAAO,YAAY,aAAa,KAAK;IACvC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,YAAY,MAAM,CAAC;AACrB;AAGO,MAAM,sBAAsB,OAAO;IACxC,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,oBAAoB;IACpB,IAAI,aAAa,YAAY,SAAS,UAAU,CAAC,cAAc;QAC7D,OAAO;IACT;IAEA,IAAI;QACF,MAAM,gBAAgB,QAAQ,OAAO,CAAC,GAAG,CAAC;QAE1C,IAAI,CAAC,eAAe;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;QAC5D;QAEA,MAAM,cAAc,KAAK,KAAK,CAAC,cAAc,KAAK;QAClD,MAAM,MAAM,KAAK,GAAG;QAEpB,iBAAiB;QACjB,IAAI,MAAM,YAAY,SAAS,GAAG,oBAAoB,CAAC,YAAY,aAAa,EAAE;YAChF,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;YACpE,SAAS,OAAO,CAAC,MAAM,CAAC;YACxB,OAAO;QACT;QAEA,OAAO,MAAM,YAAY;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;QACpE,SAAS,OAAO,CAAC,MAAM,CAAC;QACxB,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/TodoCalender/task-finance-tracker/src/app/api/auth/login/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { validateLogin, createSession } from '@/lib/auth';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { username, password } = await request.json();\n\n    if (!username || !password) {\n      return NextResponse.json(\n        { error: 'ユーザー名とパスワードが必要です' },\n        { status: 400 }\n      );\n    }\n\n    if (validate<PERSON><PERSON><PERSON>(username, password)) {\n      await createSession();\n      return NextResponse.json({ success: true });\n    } else {\n      return NextResponse.json(\n        { error: 'ユーザー名またはパスワードが正しくありません' },\n        { status: 401 }\n      );\n    }\n  } catch (error) {\n    console.error('Login API error:', error);\n    return NextResponse.json(\n      { error: 'サーバーエラーが発生しました' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEjD,IAAI,CAAC,YAAY,CAAC,UAAU;YAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE,UAAU,WAAW;YACrC,MAAM,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,SAAS;YAAK;QAC3C,OAAO;YACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAiB,GAC1B;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}