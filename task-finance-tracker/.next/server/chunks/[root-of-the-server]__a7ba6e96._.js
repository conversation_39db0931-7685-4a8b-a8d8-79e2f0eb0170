module.exports = {

"[project]/.next-internal/server/app/api/tasks/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/node:crypto [external] (node:crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}}),
"[project]/src/lib/initial-data.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "initialFinancialPlans": (()=>initialFinancialPlans),
    "initialTasks": (()=>initialTasks)
});
const initialTasks = [
    // 7月のタスク
    {
        title: '7月：顧客対応と住居確定',
        description: '住居の最終判断と顧客への退職説明',
        status: 'not_started',
        targetDate: '2024-07-31',
        priority: 'high',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        title: '内見結果を踏まえた住居の最終判断',
        description: '来週の内見後に住居を確定する',
        status: 'not_started',
        targetDate: '2024-07-07',
        priority: 'high',
        parentId: 'task_1',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        title: '購入決定なら住宅ローン等の手続き開始',
        description: '住居購入が決定した場合の手続き',
        status: 'not_started',
        targetDate: '2024-07-14',
        priority: 'high',
        parentId: 'task_1',
        financialImpact: {
            type: 'expense',
            amount: 10000000,
            description: '住居購入費用'
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        title: '顧客への退職説明を本格開始',
        description: '週2-3社ペースで顧客に説明',
        status: 'not_started',
        targetDate: '2024-07-31',
        priority: 'high',
        parentId: 'task_1',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    // 8月のタスク
    {
        title: '8月：奥様移住と収入確定',
        description: '奥様の移住サポートと退職後収入の確定',
        status: 'not_started',
        targetDate: '2024-08-31',
        priority: 'high',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        title: '奥様の移住実行とサポート',
        description: '8月前半の奥様移住サポート',
        status: 'not_started',
        targetDate: '2024-08-15',
        priority: 'high',
        parentId: 'task_5',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        title: '新居での生活環境セットアップ',
        description: '生活に必要な環境の整備',
        status: 'not_started',
        targetDate: '2024-08-15',
        priority: 'medium',
        parentId: 'task_5',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        title: '退職後収入の確定',
        description: '目標：年800-1000万円',
        status: 'not_started',
        targetDate: '2024-08-31',
        priority: 'high',
        parentId: 'task_5',
        financialImpact: {
            type: 'income',
            amount: 9000000,
            description: '退職後年収目標（平均値）'
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    // 9月のタスク
    {
        title: '9月：ご自身の移住と新体制移行',
        description: '月2回出社体制での移住と引き継ぎ完了',
        status: 'not_started',
        targetDate: '2024-09-30',
        priority: 'high',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        title: '月2回出社体制スタートに合わせて移住',
        description: '新しい働き方での移住実行',
        status: 'not_started',
        targetDate: '2024-09-15',
        priority: 'high',
        parentId: 'task_9',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        title: '仕事環境の最終調整',
        description: 'リモートワーク環境の完成',
        status: 'not_started',
        targetDate: '2024-09-20',
        priority: 'medium',
        parentId: 'task_9',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        title: '引き継ぎ完了に向けた最終段階',
        description: '業務引き継ぎの完了',
        status: 'not_started',
        targetDate: '2024-09-30',
        priority: 'high',
        parentId: 'task_9',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    }
];
const initialFinancialPlans = [
    {
        type: 'income_target',
        title: '退職後年収目標（最低）',
        amount: 8000000,
        period: 'yearly',
        targetDate: '2024-12-31',
        notes: '年800万円の収入確保',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        type: 'income_target',
        title: '退職後年収目標（理想）',
        amount: 10000000,
        period: 'yearly',
        targetDate: '2024-12-31',
        notes: '年1000万円の収入確保',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        type: 'expense_plan',
        title: '月間生活費',
        amount: 300000,
        period: 'monthly',
        notes: '基本的な生活費',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        type: 'asset',
        title: '現在の資産',
        amount: 25000000,
        period: 'one_time',
        notes: '現在保有している資産',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        type: 'expense_plan',
        title: '住居購入費用',
        amount: 10000000,
        period: 'one_time',
        targetDate: '2024-08-31',
        notes: '住居購入予算',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        type: 'expense_plan',
        title: '固定資産税・維持費',
        amount: 250000,
        period: 'yearly',
        notes: '住居関連の年間維持費',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    }
];
}}),
"[project]/src/lib/mock-kv.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MockKVClient": (()=>MockKVClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$initial$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/initial-data.ts [app-route] (ecmascript)");
;
// 開発環境用のインメモリストレージ
class MockKVStorage {
    storage = new Map();
    counters = new Map();
    async get(key) {
        return this.storage.get(key) || null;
    }
    async set(key, value) {
        this.storage.set(key, value);
    }
    async del(key) {
        this.storage.delete(key);
    }
    async incr(key) {
        const current = this.counters.get(key) || 0;
        const next = current + 1;
        this.counters.set(key, next);
        return next;
    }
    // 初期データを設定
    async initializeData() {
        // タスクデータの初期化
        const tasksWithIds = [];
        for(let i = 0; i < __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$initial$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["initialTasks"].length; i++){
            const taskData = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$initial$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["initialTasks"][i];
            const id = `task_${i + 1}`;
            tasksWithIds.push({
                ...taskData,
                id,
                // parentIdの調整
                parentId: taskData.parentId ? `task_${taskData.parentId.split('_')[1]}` : undefined
            });
        }
        // 収支計画データの初期化
        const plansWithIds = [];
        for(let i = 0; i < __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$initial$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["initialFinancialPlans"].length; i++){
            const planData = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$initial$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["initialFinancialPlans"][i];
            const id = `financial_${i + 1}`;
            plansWithIds.push({
                ...planData,
                id
            });
        }
        await this.set('tasks', tasksWithIds);
        await this.set('financial_plans', plansWithIds);
        this.counters.set('task_counter', tasksWithIds.length);
        this.counters.set('financial_counter', plansWithIds.length);
    }
}
// グローバルなモックストレージインスタンス
const mockStorage = new MockKVStorage();
class MockKVClient {
    static TASKS_KEY = 'tasks';
    static TASK_COUNTER_KEY = 'task_counter';
    static FINANCIAL_PLANS_KEY = 'financial_plans';
    static FINANCIAL_COUNTER_KEY = 'financial_counter';
    // 初期化フラグ
    static initialized = false;
    // 初期化
    static async ensureInitialized() {
        if (!this.initialized) {
            await mockStorage.initializeData();
            this.initialized = true;
        }
    }
    static async getTasks() {
        await this.ensureInitialized();
        try {
            const tasks = await mockStorage.get(this.TASKS_KEY);
            return tasks || [];
        } catch (error) {
            console.error('Error getting tasks:', error);
            return [];
        }
    }
    static async saveTasks(tasks) {
        await this.ensureInitialized();
        try {
            await mockStorage.set(this.TASKS_KEY, tasks);
        } catch (error) {
            console.error('Error saving tasks:', error);
            throw error;
        }
    }
    static async getNextTaskId() {
        await this.ensureInitialized();
        try {
            const counter = await mockStorage.incr(this.TASK_COUNTER_KEY);
            return `task_${counter}`;
        } catch (error) {
            console.error('Error generating task ID:', error);
            throw error;
        }
    }
    static async getFinancialPlans() {
        await this.ensureInitialized();
        try {
            const plans = await mockStorage.get(this.FINANCIAL_PLANS_KEY);
            return plans || [];
        } catch (error) {
            console.error('Error getting financial plans:', error);
            return [];
        }
    }
    static async saveFinancialPlans(plans) {
        await this.ensureInitialized();
        try {
            await mockStorage.set(this.FINANCIAL_PLANS_KEY, plans);
        } catch (error) {
            console.error('Error saving financial plans:', error);
            throw error;
        }
    }
    static async getNextFinancialId() {
        await this.ensureInitialized();
        try {
            const counter = await mockStorage.incr(this.FINANCIAL_COUNTER_KEY);
            return `financial_${counter}`;
        } catch (error) {
            console.error('Error generating financial ID:', error);
            throw error;
        }
    }
    static async clearAllData() {
        try {
            await Promise.all([
                mockStorage.del(this.TASKS_KEY),
                mockStorage.del(this.TASK_COUNTER_KEY),
                mockStorage.del(this.FINANCIAL_PLANS_KEY),
                mockStorage.del(this.FINANCIAL_COUNTER_KEY)
            ]);
            this.initialized = false;
        } catch (error) {
            console.error('Error clearing data:', error);
            throw error;
        }
    }
    static async healthCheck() {
        try {
            await mockStorage.set('health_check', Date.now());
            const result = await mockStorage.get('health_check');
            return result !== null;
        } catch (error) {
            console.error('Mock KV health check failed:', error);
            return false;
        }
    }
}
}}),
"[project]/src/lib/kv.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "KVClient": (()=>KVClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@vercel/kv/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$kv$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mock-kv.ts [app-route] (ecmascript)");
;
;
// 開発環境かどうかを判定
const isDevelopment = ("TURBOPACK compile-time value", "development") === 'development';
const hasKVConfig = process.env.KV_URL || process.env.KV_REST_API_URL;
class KVClient {
    // タスク関連のキー
    static TASKS_KEY = 'tasks';
    static TASK_COUNTER_KEY = 'task_counter';
    // 収支関連のキー
    static FINANCIAL_PLANS_KEY = 'financial_plans';
    static FINANCIAL_COUNTER_KEY = 'financial_counter';
    // タスクデータの取得
    static async getTasks() {
        if (isDevelopment && !hasKVConfig) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$kv$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MockKVClient"].getTasks();
        }
        try {
            const tasks = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].get(this.TASKS_KEY);
            return tasks || [];
        } catch (error) {
            console.error('Error getting tasks:', error);
            return [];
        }
    }
    // タスクデータの保存
    static async saveTasks(tasks) {
        if (isDevelopment && !hasKVConfig) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$kv$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MockKVClient"].saveTasks(tasks);
        }
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].set(this.TASKS_KEY, tasks);
        } catch (error) {
            console.error('Error saving tasks:', error);
            throw error;
        }
    }
    // 新しいタスクIDの生成
    static async getNextTaskId() {
        if (isDevelopment && !hasKVConfig) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$kv$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MockKVClient"].getNextTaskId();
        }
        try {
            const counter = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].incr(this.TASK_COUNTER_KEY);
            return `task_${counter}`;
        } catch (error) {
            console.error('Error generating task ID:', error);
            throw error;
        }
    }
    // 収支計画データの取得
    static async getFinancialPlans() {
        if (isDevelopment && !hasKVConfig) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$kv$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MockKVClient"].getFinancialPlans();
        }
        try {
            const plans = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].get(this.FINANCIAL_PLANS_KEY);
            return plans || [];
        } catch (error) {
            console.error('Error getting financial plans:', error);
            return [];
        }
    }
    // 収支計画データの保存
    static async saveFinancialPlans(plans) {
        if (isDevelopment && !hasKVConfig) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$kv$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MockKVClient"].saveFinancialPlans(plans);
        }
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].set(this.FINANCIAL_PLANS_KEY, plans);
        } catch (error) {
            console.error('Error saving financial plans:', error);
            throw error;
        }
    }
    // 新しい収支計画IDの生成
    static async getNextFinancialId() {
        if (isDevelopment && !hasKVConfig) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$kv$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MockKVClient"].getNextFinancialId();
        }
        try {
            const counter = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].incr(this.FINANCIAL_COUNTER_KEY);
            return `financial_${counter}`;
        } catch (error) {
            console.error('Error generating financial ID:', error);
            throw error;
        }
    }
    // 開発用：全データのクリア
    static async clearAllData() {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        if (isDevelopment && !hasKVConfig) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$kv$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MockKVClient"].clearAllData();
        }
        try {
            await Promise.all([
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].del(this.TASKS_KEY),
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].del(this.TASK_COUNTER_KEY),
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].del(this.FINANCIAL_PLANS_KEY),
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].del(this.FINANCIAL_COUNTER_KEY)
            ]);
        } catch (error) {
            console.error('Error clearing data:', error);
            throw error;
        }
    }
    // ヘルスチェック
    static async healthCheck() {
        if (isDevelopment && !hasKVConfig) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$kv$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MockKVClient"].healthCheck();
        }
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].set('health_check', Date.now(), {
                ex: 60
            }); // 60秒で期限切れ
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$kv$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["kv"].get('health_check');
            return result !== null;
        } catch (error) {
            console.error('KV health check failed:', error);
            return false;
        }
    }
}
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "checkAuthMiddleware": (()=>checkAuthMiddleware),
    "createSession": (()=>createSession),
    "destroySession": (()=>destroySession),
    "validateLogin": (()=>validateLogin),
    "validateSession": (()=>validateSession)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
;
// 認証設定
const AUTH_COOKIE_NAME = 'auth-session';
const SESSION_DURATION = 24 * 60 * 60 * 1000; // 24時間
// 環境変数から認証情報を取得
const getAuthCredentials = ()=>{
    const username = process.env.AUTH_USERNAME;
    const password = process.env.AUTH_PASSWORD;
    if (!username || !password) {
        throw new Error('AUTH_USERNAME and AUTH_PASSWORD must be set in environment variables');
    }
    return {
        username,
        password
    };
};
const validateLogin = (username, password)=>{
    try {
        const credentials = getAuthCredentials();
        return username === credentials.username && password === credentials.password;
    } catch (error) {
        console.error('Auth validation error:', error);
        return false;
    }
};
const createSession = async ()=>{
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    const sessionData = {
        authenticated: true,
        timestamp: Date.now()
    };
    cookieStore.set(AUTH_COOKIE_NAME, JSON.stringify(sessionData), {
        httpOnly: true,
        secure: ("TURBOPACK compile-time value", "development") === 'production',
        sameSite: 'lax',
        maxAge: SESSION_DURATION / 1000
    });
};
const validateSession = async ()=>{
    try {
        const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
        const sessionCookie = cookieStore.get(AUTH_COOKIE_NAME);
        if (!sessionCookie) {
            return false;
        }
        const sessionData = JSON.parse(sessionCookie.value);
        const now = Date.now();
        // セッションの有効期限チェック
        if (now - sessionData.timestamp > SESSION_DURATION) {
            return false;
        }
        return sessionData.authenticated === true;
    } catch (error) {
        console.error('Session validation error:', error);
        return false;
    }
};
const destroySession = async ()=>{
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    cookieStore.delete(AUTH_COOKIE_NAME);
};
const checkAuthMiddleware = async (request)=>{
    const { pathname } = request.nextUrl;
    // ログインページとAPIルートは除外
    if (pathname === '/login' || pathname.startsWith('/api/auth')) {
        return null;
    }
    try {
        const sessionCookie = request.cookies.get(AUTH_COOKIE_NAME);
        if (!sessionCookie) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/login', request.url));
        }
        const sessionData = JSON.parse(sessionCookie.value);
        const now = Date.now();
        // セッションの有効期限チェック
        if (now - sessionData.timestamp > SESSION_DURATION || !sessionData.authenticated) {
            const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/login', request.url));
            response.cookies.delete(AUTH_COOKIE_NAME);
            return response;
        }
        return null; // 認証OK、処理続行
    } catch (error) {
        console.error('Middleware auth error:', error);
        const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/login', request.url));
        response.cookies.delete(AUTH_COOKIE_NAME);
        return response;
    }
};
}}),
"[project]/src/app/api/tasks/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$kv$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/kv.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
;
;
;
async function GET(request) {
    try {
        // 認証チェック
        const isAuthenticated = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateSession"])();
        if (!isAuthenticated) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: '認証が必要です'
            }, {
                status: 401
            });
        }
        const { searchParams } = new URL(request.url);
        const parentId = searchParams.get('parentId');
        const status = searchParams.get('status');
        const priority = searchParams.get('priority');
        let tasks = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$kv$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["KVClient"].getTasks();
        // フィルタリング
        if (parentId) {
            tasks = tasks.filter((task)=>task.parentId === parentId);
        }
        if (status) {
            tasks = tasks.filter((task)=>task.status === status);
        }
        if (priority) {
            tasks = tasks.filter((task)=>task.priority === priority);
        }
        // 作成日時でソート
        tasks.sort((a, b)=>new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            tasks
        });
    } catch (error) {
        console.error('Error fetching tasks:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'タスクの取得に失敗しました'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        // 認証チェック
        const isAuthenticated = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateSession"])();
        if (!isAuthenticated) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: '認証が必要です'
            }, {
                status: 401
            });
        }
        const body = await request.json();
        const { title, description, targetDate, priority, parentId, financialImpact } = body;
        // バリデーション
        if (!title || !title.trim()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'タイトルは必須です'
            }, {
                status: 400
            });
        }
        if (priority && ![
            'low',
            'medium',
            'high'
        ].includes(priority)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: '優先度は low, medium, high のいずれかである必要があります'
            }, {
                status: 400
            });
        }
        // 新しいタスクを作成
        const id = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$kv$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["KVClient"].getNextTaskId();
        const now = new Date().toISOString();
        const newTask = {
            id,
            title: title.trim(),
            description: description?.trim() || undefined,
            status: 'not_started',
            targetDate: targetDate || undefined,
            priority: priority || 'medium',
            parentId: parentId || undefined,
            financialImpact: financialImpact || undefined,
            createdAt: now,
            updatedAt: now
        };
        // 既存のタスクを取得して新しいタスクを追加
        const tasks = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$kv$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["KVClient"].getTasks();
        tasks.push(newTask);
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$kv$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["KVClient"].saveTasks(tasks);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            task: newTask
        }, {
            status: 201
        });
    } catch (error) {
        console.error('Error creating task:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'タスクの作成に失敗しました'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__a7ba6e96._.js.map