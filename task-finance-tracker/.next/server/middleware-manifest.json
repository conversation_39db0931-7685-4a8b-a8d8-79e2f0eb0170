{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_d7c7841b._.js", "server/edge/chunks/[root-of-the-server]__c28bd568._.js", "server/edge/chunks/edge-wrapper_c91542f8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|login).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|login).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "XZkPj/0feIj7kiVEsbrkntKD6r2uXNrs5V/sRi5Jk6M=", "__NEXT_PREVIEW_MODE_ID": "47a6eb01db36b514027d8667fb24dcf5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "992f6c837be3bc51b7d1ae5fa8103b4fa96c3d039acdf0ab81f31dc4cc957f6f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b470cd30b441fe7ca1f0064722ab839cf8a621b0aad60402bda97cc7cb5cecf4"}}}, "sortedMiddleware": ["/"], "functions": {}}