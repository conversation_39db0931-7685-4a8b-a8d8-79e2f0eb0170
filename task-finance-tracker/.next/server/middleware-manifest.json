{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_d7c7841b._.js", "server/edge/chunks/[root-of-the-server]__c28bd568._.js", "server/edge/chunks/edge-wrapper_c91542f8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|login).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|login).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "XZkPj/0feIj7kiVEsbrkntKD6r2uXNrs5V/sRi5Jk6M=", "__NEXT_PREVIEW_MODE_ID": "132eac1558b46fd0837509a646b65071", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2b4f17b70fa9e9034a7d172da9a939c75811f2acac1c93eafdd2e94268dce3d9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c5446f01e5f56029d93594261f02d517e57b492f058ed4f63b92f04e6c6820dc"}}}, "sortedMiddleware": ["/"], "functions": {}}