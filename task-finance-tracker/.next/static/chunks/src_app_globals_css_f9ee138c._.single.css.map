{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/globals.css"], "sourcesContent": ["/* ===== RESET & BASE STYLES ===== */\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nhtml, body {\n  height: 100%;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Open Sans', 'Helvetica Neue', sans-serif;\n  line-height: 1.6;\n  color: #2d3748;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  min-height: 100vh;\n}\n\n/* ===== LAYOUT ===== */\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 1rem;\n}\n\n.flex {\n  display: flex;\n}\n\n.flex-col {\n  flex-direction: column;\n}\n\n.items-center {\n  align-items: center;\n}\n\n.justify-center {\n  justify-content: center;\n}\n\n.justify-between {\n  justify-content: space-between;\n}\n\n.space-x-4 > * + * {\n  margin-left: 1rem;\n}\n\n.space-y-4 > * + * {\n  margin-top: 1rem;\n}\n\n.space-y-6 > * + * {\n  margin-top: 1.5rem;\n}\n\n.grid {\n  display: grid;\n}\n\n.grid-cols-1 {\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\n\n.grid-cols-2 {\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n}\n\n.grid-cols-3 {\n  grid-template-columns: repeat(3, minmax(0, 1fr));\n}\n\n.gap-4 {\n  gap: 1rem;\n}\n\n.gap-6 {\n  gap: 1.5rem;\n}\n\n/* ===== CARDS ===== */\n.card {\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(20px);\n          backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 16px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  padding: 1.5rem;\n  transition: all 0.3s ease;\n}\n\n.card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\n}\n\n/* ===== BUTTONS ===== */\n.btn {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 12px;\n  font-size: 0.875rem;\n  font-weight: 600;\n  text-decoration: none;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.btn:before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  transition: left 0.5s;\n}\n\n.btn:hover:before {\n  left: 100%;\n}\n\n.btn-primary {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);\n}\n\n.btn-primary:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);\n}\n\n.btn-secondary {\n  background: rgba(255, 255, 255, 0.9);\n  color: #4a5568;\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.btn-secondary:hover {\n  background: white;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none !important;\n}\n\n.btn-sm {\n  padding: 0.5rem 1rem;\n  font-size: 0.75rem;\n}\n\n.btn-lg {\n  padding: 1rem 2rem;\n  font-size: 1rem;\n}\n\n/* ===== FORMS ===== */\n.form-input {\n  width: 100%;\n  padding: 0.75rem 1rem;\n  background: rgba(255, 255, 255, 0.9);\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-radius: 12px;\n  font-size: 0.875rem;\n  transition: all 0.3s ease;\n  -webkit-backdrop-filter: blur(10px);\n          backdrop-filter: blur(10px);\n}\n\n.form-input:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n  transform: translateY(-1px);\n}\n\n.form-input::-moz-placeholder {\n  color: #a0aec0;\n}\n\n.form-input::placeholder {\n  color: #a0aec0;\n}\n\n.form-label {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-size: 0.875rem;\n  font-weight: 600;\n  color: #4a5568;\n}\n\n.form-group {\n  margin-bottom: 1.5rem;\n}\n\n/* ===== NAVIGATION ===== */\n.navbar {\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(20px);\n          backdrop-filter: blur(20px);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n  padding: 1rem 0;\n}\n\n.nav-link {\n  color: #4a5568;\n  text-decoration: none;\n  padding: 0.5rem 1rem;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n  font-weight: 500;\n}\n\n.nav-link:hover {\n  background: rgba(102, 126, 234, 0.1);\n  color: #667eea;\n}\n\n.nav-link.active {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n/* ===== TYPOGRAPHY ===== */\nh1 {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #2d3748;\n  margin-bottom: 0.5rem;\n}\n\nh2 {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #2d3748;\n  margin-bottom: 1rem;\n}\n\nh3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #2d3748;\n  margin-bottom: 0.75rem;\n}\n\np {\n  color: #718096;\n  margin-bottom: 1rem;\n}\n\n/* ===== UTILITIES ===== */\n.text-center {\n  text-align: center;\n}\n\n.text-white {\n  color: white;\n}\n\n.text-gray-600 {\n  color: #718096;\n}\n\n.text-gray-900 {\n  color: #2d3748;\n}\n\n.bg-white {\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(20px);\n          backdrop-filter: blur(20px);\n}\n\n.shadow {\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n}\n\n.shadow-lg {\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\n}\n\n.shadow-xl {\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);\n}\n\n.rounded {\n  border-radius: 8px;\n}\n\n.rounded-lg {\n  border-radius: 12px;\n}\n\n.rounded-xl {\n  border-radius: 16px;\n}\n\n.p-4 {\n  padding: 1rem;\n}\n\n.p-6 {\n  padding: 1.5rem;\n}\n\n.p-8 {\n  padding: 2rem;\n}\n\n.px-4 {\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\n\n.py-2 {\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\n\n.py-4 {\n  padding-top: 1rem;\n  padding-bottom: 1rem;\n}\n\n.py-8 {\n  padding-top: 2rem;\n  padding-bottom: 2rem;\n}\n\n.mb-4 {\n  margin-bottom: 1rem;\n}\n\n.mb-6 {\n  margin-bottom: 1.5rem;\n}\n\n.mb-8 {\n  margin-bottom: 2rem;\n}\n\n.mt-8 {\n  margin-top: 2rem;\n}\n\n.w-full {\n  width: 100%;\n}\n\n.max-w-md {\n  max-width: 28rem;\n}\n\n.max-w-7xl {\n  max-width: 80rem;\n}\n\n.min-h-screen {\n  min-height: 100vh;\n}\n\n.hidden {\n  display: none;\n}\n\n.block {\n  display: block;\n}\n\n.inline-flex {\n  display: inline-flex;\n}\n\n/* ===== RESPONSIVE ===== */\n@media (min-width: 640px) {\n  .sm\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .sm\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .sm\\:px-6 {\n    padding-left: 1.5rem;\n    padding-right: 1.5rem;\n  }\n\n  .sm\\:block {\n    display: block;\n  }\n\n  .sm\\:hidden {\n    display: none;\n  }\n}\n\n@media (min-width: 1024px) {\n  .lg\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .lg\\:px-8 {\n    padding-left: 2rem;\n    padding-right: 2rem;\n  }\n}\n\n/* ===== COMPONENT SPECIFIC ===== */\n.task-item {\n  background: rgba(255, 255, 255, 0.9);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin-bottom: 1rem;\n  transition: all 0.3s ease;\n  -webkit-backdrop-filter: blur(10px);\n          backdrop-filter: blur(10px);\n}\n\n.task-item:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n}\n\n.task-item.completed {\n  opacity: 0.7;\n}\n\n.task-item.completed .task-title {\n  text-decoration: line-through;\n  color: #a0aec0;\n}\n\n.task-title {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #2d3748;\n  margin-bottom: 0.5rem;\n}\n\n.task-description {\n  color: #718096;\n  margin-bottom: 1rem;\n}\n\n.status-badge {\n  display: inline-block;\n  padding: 0.25rem 0.75rem;\n  border-radius: 20px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n\n.status-not-started {\n  background: rgba(160, 174, 192, 0.2);\n  color: #4a5568;\n}\n\n.status-in-progress {\n  background: rgba(102, 126, 234, 0.2);\n  color: #667eea;\n}\n\n.status-completed {\n  background: rgba(72, 187, 120, 0.2);\n  color: #48bb78;\n}\n\n.priority-high {\n  background: rgba(245, 101, 101, 0.2);\n  color: #f56565;\n}\n\n.priority-medium {\n  background: rgba(237, 137, 54, 0.2);\n  color: #ed8936;\n}\n\n.priority-low {\n  background: rgba(160, 174, 192, 0.2);\n  color: #a0aec0;\n}\n\n.dialog-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  -webkit-backdrop-filter: blur(4px);\n          backdrop-filter: blur(4px);\n  z-index: 50;\n}\n\n.dialog-content {\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(20px);\n          backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 20px;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n  max-width: 90vw;\n  max-height: 90vh;\n  overflow-y: auto;\n  z-index: 51;\n}\n\n/* ===== ANIMATIONS ===== */\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.fade-in {\n  animation: fadeIn 0.5s ease-out;\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateX(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n.slide-in {\n  animation: slideIn 0.3s ease-out;\n}\n\n\n"], "names": [], "mappings": "AACA;;;;;;AAMA;;;;;;;;;AAUA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;;;;;;AAWA;;;;;AAMA;;;;;;;;;;;;;;;;AAgBA;;;;;;;;;;;AAWA;;;;AAIA;;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;AAMA;;;;;;;;;;;AAYA;;;;;;;AAOA;;;;AAAA;;;;AAQA;;;;;;;;AAQA;;;;AAKA;;;;;;;;AASA;;;;;;;;;AASA;;;;;AAKA;;;;;AAMA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;;;AAOF;;;;;;;;;;AAWA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;;;AAOA;;;;;AAKA;;;;;;;;;;AAUA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;;;AAYA;;;;;;;;;;;;;;;;AAkBA;;;;;;;;;;;;AAWA;;;;AAIA;;;;;;;;;;;;AAWA"}}]}