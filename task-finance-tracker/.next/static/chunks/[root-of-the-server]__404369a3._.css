/* [next]/internal/font/google/inter_9e72d27f.module.css [app-client] (css) */
@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n_wU-s.91b7455f.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n_wU-s.927aef78.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n_wU-s.b7398c1c.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n_wU-s.ac666cb5.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n_wU-s.569fab99.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n_wU-s.99c7dd4e.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw-s.p.0faac26c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter Fallback;
  src: local(Arial);
  ascent-override: 90.44%;
  descent-override: 22.52%;
  line-gap-override: 0.0%;
  size-adjust: 107.12%;
}

.inter_9e72d27f-module__JKMi0a__className {
  font-family: Inter, Inter Fallback;
  font-style: normal;
}

.inter_9e72d27f-module__JKMi0a__variable {
  --font-inter: "Inter", "Inter Fallback";
}


/* [project]/src/app/globals.css [app-client] (css) */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  color: #2d3748;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  height: 100%;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Open Sans, Helvetica Neue, sans-serif;
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.card {
  backdrop-filter: blur(20px);
  background: #fffffff2;
  border: 1px solid #fff3;
  border-radius: 16px;
  padding: 1.5rem;
  transition: all .3s;
  box-shadow: 0 8px 32px #0000001a;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px #00000026;
}

.btn {
  cursor: pointer;
  border: none;
  border-radius: 12px;
  justify-content: center;
  align-items: center;
  padding: .75rem 1.5rem;
  font-size: .875rem;
  font-weight: 600;
  text-decoration: none;
  transition: all .3s;
  display: inline-flex;
  position: relative;
  overflow: hidden;
}

.btn:before {
  content: "";
  background: linear-gradient(90deg, #0000, #fff3, #0000);
  width: 100%;
  height: 100%;
  transition: left .5s;
  position: absolute;
  top: 0;
  left: -100%;
}

.btn:hover:before {
  left: 100%;
}

.btn-primary {
  color: #fff;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 15px #667eea66;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #667eea99;
}

.btn-secondary {
  color: #4a5568;
  background: #ffffffe6;
  border: 1px solid #ffffff4d;
  box-shadow: 0 2px 8px #0000001a;
}

.btn-secondary:hover {
  background: #fff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px #00000026;
}

.btn:disabled {
  opacity: .6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-sm {
  padding: .5rem 1rem;
  font-size: .75rem;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1rem;
}

.form-input {
  backdrop-filter: blur(10px);
  background: #ffffffe6;
  border: 2px solid #ffffff4d;
  border-radius: 12px;
  width: 100%;
  padding: .75rem 1rem;
  font-size: .875rem;
  transition: all .3s;
}

.form-input:focus {
  border-color: #667eea;
  outline: none;
  transform: translateY(-1px);
  box-shadow: 0 0 0 3px #667eea1a;
}

.form-input::-moz-placeholder {
  color: #a0aec0;
}

.form-input::placeholder {
  color: #a0aec0;
}

.form-label {
  color: #4a5568;
  margin-bottom: .5rem;
  font-size: .875rem;
  font-weight: 600;
  display: block;
}

.form-group {
  margin-bottom: 1.5rem;
}

.navbar {
  backdrop-filter: blur(20px);
  background: #fffffff2;
  border-bottom: 1px solid #fff3;
  padding: 1rem 0;
  box-shadow: 0 4px 20px #0000001a;
}

.nav-link {
  color: #4a5568;
  border-radius: 8px;
  padding: .5rem 1rem;
  font-weight: 500;
  text-decoration: none;
  transition: all .3s;
}

.nav-link:hover {
  color: #667eea;
  background: #667eea1a;
}

.nav-link.active {
  color: #fff;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

h1 {
  color: #2d3748;
  margin-bottom: .5rem;
  font-size: 2rem;
  font-weight: 700;
}

h2 {
  color: #2d3748;
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
}

h3 {
  color: #2d3748;
  margin-bottom: .75rem;
  font-size: 1.25rem;
  font-weight: 600;
}

p {
  color: #718096;
  margin-bottom: 1rem;
}

.text-center {
  text-align: center;
}

.text-white {
  color: #fff;
}

.text-gray-600 {
  color: #718096;
}

.text-gray-900 {
  color: #2d3748;
}

.bg-white {
  backdrop-filter: blur(20px);
  background: #fffffff2;
}

.shadow {
  box-shadow: 0 4px 20px #0000001a;
}

.shadow-lg {
  box-shadow: 0 8px 30px #00000026;
}

.shadow-xl {
  box-shadow: 0 20px 40px #0003;
}

.rounded {
  border-radius: 8px;
}

.rounded-lg {
  border-radius: 12px;
}

.rounded-xl {
  border-radius: 16px;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-2 {
  padding-top: .5rem;
  padding-bottom: .5rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mt-8 {
  margin-top: 2rem;
}

.w-full {
  width: 100%;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-7xl {
  max-width: 80rem;
}

.min-h-screen {
  min-height: 100vh;
}

.hidden {
  display: none;
}

.block {
  display: block;
}

.inline-flex {
  display: inline-flex;
}

@media (width >= 640px) {
  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:block {
    display: block;
  }

  .sm\:hidden {
    display: none;
  }
}

@media (width >= 1024px) {
  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.task-item {
  backdrop-filter: blur(10px);
  background: #ffffffe6;
  border: 1px solid #ffffff4d;
  border-radius: 12px;
  margin-bottom: 1rem;
  padding: 1.5rem;
  transition: all .3s;
}

.task-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #0000001a;
}

.task-item.completed {
  opacity: .7;
}

.task-item.completed .task-title {
  color: #a0aec0;
  text-decoration: line-through;
}

.task-title {
  color: #2d3748;
  margin-bottom: .5rem;
  font-size: 1.125rem;
  font-weight: 600;
}

.task-description {
  color: #718096;
  margin-bottom: 1rem;
}

.status-badge {
  text-transform: uppercase;
  letter-spacing: .05em;
  border-radius: 20px;
  padding: .25rem .75rem;
  font-size: .75rem;
  font-weight: 600;
  display: inline-block;
}

.status-not-started {
  color: #4a5568;
  background: #a0aec033;
}

.status-in-progress {
  color: #667eea;
  background: #667eea33;
}

.status-completed {
  color: #48bb78;
  background: #48bb7833;
}

.priority-high {
  color: #f56565;
  background: #f5656533;
}

.priority-medium {
  color: #ed8936;
  background: #ed893633;
}

.priority-low {
  color: #a0aec0;
  background: #a0aec033;
}

.dialog-overlay {
  backdrop-filter: blur(4px);
  z-index: 50;
  background: #00000080;
  position: fixed;
  inset: 0;
}

.dialog-content {
  backdrop-filter: blur(20px);
  z-index: 51;
  background: #fffffff2;
  border: 1px solid #fff3;
  border-radius: 20px;
  max-width: 90vw;
  max-height: 90vh;
  position: fixed;
  top: 50%;
  left: 50%;
  overflow-y: auto;
  transform: translate(-50%, -50%);
  box-shadow: 0 20px 60px #0000004d;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: .5s ease-out fadeIn;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in {
  animation: .3s ease-out slideIn;
}


/*# sourceMappingURL=%5Broot-of-the-server%5D__404369a3._.css.map*/