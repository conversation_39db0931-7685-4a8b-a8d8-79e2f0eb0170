{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.module.css"], "sourcesContent": ["/* cyrillic-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* greek-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+1F00-1FFF;\n}\n/* greek */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Inter Fallback';\n    src: local(\"Arial\");\n    ascent-override: 90.44%;\ndescent-override: 22.52%;\nline-gap-override: 0.00%;\nsize-adjust: 107.12%;\n\n}\n.className {\n    font-family: 'Inter', 'Inter Fallback';\n    font-style: normal;\n\n}\n.variable {\n    --font-inter: 'Inter', 'Inter Fallback';\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/TodoCalender/task-finance-tracker/src/app/globals.css"], "sourcesContent": ["/*! tailwindcss v4.1.10 | MIT License | https://tailwindcss.com */\n@layer properties;\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n.absolute {\n  position: absolute;\n}\n.fixed {\n  position: fixed;\n}\n.relative {\n  position: relative;\n}\n.static {\n  position: static;\n}\n.z-50 {\n  z-index: 50;\n}\n.mx-auto {\n  margin-inline: auto;\n}\n.block {\n  display: block;\n}\n.flex {\n  display: flex;\n}\n.grid {\n  display: grid;\n}\n.hidden {\n  display: none;\n}\n.inline-flex {\n  display: inline-flex;\n}\n.min-h-screen {\n  min-height: 100vh;\n}\n.w-full {\n  width: 100%;\n}\n.flex-1 {\n  flex: 1;\n}\n.flex-shrink-0 {\n  flex-shrink: 0;\n}\n.cursor-pointer {\n  cursor: pointer;\n}\n.appearance-none {\n  appearance: none;\n}\n.grid-cols-1 {\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\n.flex-wrap {\n  flex-wrap: wrap;\n}\n.items-center {\n  align-items: center;\n}\n.items-end {\n  align-items: flex-end;\n}\n.items-start {\n  align-items: flex-start;\n}\n.justify-between {\n  justify-content: space-between;\n}\n.justify-center {\n  justify-content: center;\n}\n.justify-end {\n  justify-content: flex-end;\n}\n.-space-y-px {\n  :where(& > :not(:last-child)) {\n    --tw-space-y-reverse: 0;\n    margin-block-start: calc(-1px * var(--tw-space-y-reverse));\n    margin-block-end: calc(-1px * calc(1 - var(--tw-space-y-reverse)));\n  }\n}\n.truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.overflow-hidden {\n  overflow: hidden;\n}\n.rounded-full {\n  border-radius: calc(infinity * 1px);\n}\n.rounded-none {\n  border-radius: 0;\n}\n.border {\n  border-style: var(--tw-border-style);\n  border-width: 1px;\n}\n.border-0 {\n  border-style: var(--tw-border-style);\n  border-width: 0px;\n}\n.border-t {\n  border-top-style: var(--tw-border-style);\n  border-top-width: 1px;\n}\n.border-b {\n  border-bottom-style: var(--tw-border-style);\n  border-bottom-width: 1px;\n}\n.border-b-2 {\n  border-bottom-style: var(--tw-border-style);\n  border-bottom-width: 2px;\n}\n.border-l-2 {\n  border-left-style: var(--tw-border-style);\n  border-left-width: 2px;\n}\n.border-l-4 {\n  border-left-style: var(--tw-border-style);\n  border-left-width: 4px;\n}\n.border-transparent {\n  border-color: transparent;\n}\n.text-center {\n  text-align: center;\n}\n.text-left {\n  text-align: left;\n}\n.line-through {\n  text-decoration-line: line-through;\n}\n.antialiased {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n.ring-1 {\n  --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n}\n.transition-all {\n  transition-property: all;\n  transition-timing-function: var(--tw-ease, ease);\n  transition-duration: var(--tw-duration, 0s);\n}\n.transition-colors {\n  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n  transition-timing-function: var(--tw-ease, ease);\n  transition-duration: var(--tw-duration, 0s);\n}\n.transition-shadow {\n  transition-property: box-shadow;\n  transition-timing-function: var(--tw-ease, ease);\n  transition-duration: var(--tw-duration, 0s);\n}\n.duration-300 {\n  --tw-duration: 300ms;\n  transition-duration: 300ms;\n}\n.ring-inset {\n  --tw-ring-inset: inset;\n}\n.focus\\:z-10 {\n  &:focus {\n    z-index: 10;\n  }\n}\n.focus\\:ring-2 {\n  &:focus {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n}\n.focus\\:ring-offset-2 {\n  &:focus {\n    --tw-ring-offset-width: 2px;\n    --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  }\n}\n.focus\\:outline-none {\n  &:focus {\n    --tw-outline-style: none;\n    outline-style: none;\n  }\n}\n.focus\\:ring-inset {\n  &:focus {\n    --tw-ring-inset: inset;\n  }\n}\n.disabled\\:cursor-not-allowed {\n  &:disabled {\n    cursor: not-allowed;\n  }\n}\n.disabled\\:opacity-50 {\n  &:disabled {\n    opacity: 50%;\n  }\n}\n* {\n  box-sizing: border-box;\n}\nhtml,\nbody {\n  max-width: 100vw;\n  overflow-x: hidden;\n}\nbody {\n  background-color: #ffffff;\n  color: #000000;\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;\n  line-height: 1.6;\n}\ninput[type=\"text\"],\ninput[type=\"email\"],\ninput[type=\"password\"],\ninput[type=\"number\"],\ninput[type=\"date\"],\ntextarea,\nselect {\n  background-color: #ffffff !important;\n  border: 1px solid #000000 !important;\n  color: #000000 !important;\n  border-radius: 0.375rem;\n  padding: 0.5rem 0.75rem;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n}\ninput[type=\"text\"]:focus,\ninput[type=\"email\"]:focus,\ninput[type=\"password\"]:focus,\ninput[type=\"number\"]:focus,\ninput[type=\"date\"]:focus,\ntextarea:focus,\nselect:focus {\n  outline: none;\n  border-color: #000000 !important;\n  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1) !important;\n}\ninput[type=\"text\"]:disabled,\ninput[type=\"email\"]:disabled,\ninput[type=\"password\"]:disabled,\ninput[type=\"number\"]:disabled,\ninput[type=\"date\"]:disabled,\ntextarea:disabled,\nselect:disabled {\n  background-color: #f3f4f6 !important;\n  color: #6b7280 !important;\n  cursor: not-allowed;\n}\nbutton {\n  background-color: #ffffff !important;\n  border: 1px solid #000000 !important;\n  color: #000000 !important;\n  cursor: pointer;\n  font-family: inherit;\n  font-size: inherit;\n  line-height: inherit;\n  border-radius: 0.375rem;\n}\nbutton:hover {\n  background-color: #f3f4f6 !important;\n}\n.btn-primary {\n  background-color: #ffffff !important;\n  color: #000000 !important;\n  border: 1px solid #000000 !important;\n  border-radius: 0.375rem;\n  padding: 0.5rem 1rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  transition: background-color 0.15s ease-in-out;\n}\n.btn-primary:hover {\n  background-color: #f3f4f6 !important;\n  color: #000000 !important;\n}\n.btn-primary:disabled {\n  background-color: #f3f4f6 !important;\n  color: #6b7280 !important;\n  cursor: not-allowed;\n}\n.bg-indigo-600,\n.bg-indigo-700 {\n  background-color: #ffffff !important;\n  color: #000000 !important;\n  border: 1px solid #000000 !important;\n}\n.bg-indigo-600 *,\n.bg-indigo-700 * {\n  color: #000000 !important;\n}\n.btn-secondary {\n  background-color: #ffffff !important;\n  color: #000000 !important;\n  border: 1px solid #000000 !important;\n  border-radius: 0.375rem;\n  padding: 0.5rem 1rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  transition: background-color 0.15s ease-in-out;\n}\n.btn-secondary:hover {\n  background-color: #f3f4f6 !important;\n}\na {\n  color: #000000 !important;\n  text-decoration: none;\n  transition: color 0.15s ease-in-out;\n}\na:hover {\n  color: #000000 !important;\n  text-decoration: underline;\n}\n.card {\n  background-color: #ffffff !important;\n  border: 1px solid #000000 !important;\n  border-radius: 0.5rem;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\n}\n[role=\"dialog\"] {\n  background-color: #ffffff !important;\n  color: #000000 !important;\n}\nsvg {\n  color: #000000 !important;\n}\nbutton, a, select, input, textarea {\n  transition: all 0.15s ease-in-out;\n}\nbutton:focus-visible,\na:focus-visible,\ninput:focus-visible,\ntextarea:focus-visible,\nselect:focus-visible {\n  outline: 2px solid #000000 !important;\n  outline-offset: 2px;\n}\nbutton:disabled,\ninput:disabled,\ntextarea:disabled,\nselect:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n.text-gray-500,\n.text-gray-600,\n.text-gray-700,\n.text-gray-800,\n.text-gray-900 {\n  color: #000000 !important;\n}\n.h-3, .h-4, .h-5, .h-6 {\n  color: #000000 !important;\n}\nbutton.bg-indigo-600 *,\nbutton.hover\\:bg-indigo-700 *,\nbutton.bg-indigo-700 *,\nbutton * {\n  color: #000000 !important;\n}\n.max-w-md {\n  max-width: 28rem;\n}\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\n}\n.bg-gray-50 {\n  background-color: #ffffff !important;\n}\n.-space-y-px > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(-1px * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(-1px * var(--tw-space-y-reverse));\n}\n.bg-white {\n  background-color: #ffffff !important;\n}\n::placeholder {\n  color: #6b7280 !important;\n  opacity: 1;\n}\n.placeholder-gray-500 {\n  color: #6b7280 !important;\n}\n.text-white {\n  color: #000000 !important;\n}\n.bg-indigo-600 {\n  background-color: #ffffff !important;\n}\n.hover\\:bg-indigo-700:hover {\n  background-color: #f3f4f6 !important;\n}\n.text-indigo-600,\n.text-indigo-700,\n.text-red-600,\n.text-red-700,\n.text-blue-600,\n.text-blue-700,\n.text-green-600,\n.text-green-700 {\n  color: #000000 !important;\n}\n.bg-gray-100,\n.bg-blue-100,\n.bg-green-100,\n.bg-red-100,\n.bg-yellow-100 {\n  background-color: #ffffff !important;\n  border: 1px solid #000000 !important;\n}\n.text-gray-800,\n.text-blue-800,\n.text-green-800,\n.text-red-800,\n.text-yellow-800 {\n  color: #000000 !important;\n}\n@property --tw-space-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-inset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-ring-inset {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-offset-width {\n  syntax: \"<length>\";\n  inherits: false;\n  initial-value: 0px;\n}\n@property --tw-ring-offset-color {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: #fff;\n}\n@property --tw-ring-offset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-space-y-reverse: 0;\n      --tw-border-style: solid;\n      --tw-shadow: 0 0 #0000;\n      --tw-shadow-color: initial;\n      --tw-shadow-alpha: 100%;\n      --tw-inset-shadow: 0 0 #0000;\n      --tw-inset-shadow-color: initial;\n      --tw-inset-shadow-alpha: 100%;\n      --tw-ring-color: initial;\n      --tw-ring-shadow: 0 0 #0000;\n      --tw-inset-ring-color: initial;\n      --tw-inset-ring-shadow: 0 0 #0000;\n      --tw-ring-inset: initial;\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-color: #fff;\n      --tw-ring-offset-shadow: 0 0 #0000;\n      --tw-duration: initial;\n    }\n  }\n}\n"], "names": [], "mappings": "AACA;EAugBE;IACE;;;;;;;;;;;;;;;;;;;;;;AAvgBJ;;;;;;;;;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIE;;;;;;AAMF;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;AAIE;;;;AAKA;;;;;AAMA;;;;;AAMA;;;;;AAMA;;;;AAKA;;;;AAKA;;;;AAIF;;;;AAGA;;;;;AAKA;;;;;;;AAMA;;;;;;;;;;;AAgBA;;;;;;AAWA;;;;;;AAWA;;;;;;;;;;;AAUA;;;;AAGA;;;;;;;;;;;AAUA;;;;;AAIA;;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;;;;;;;;AAUA;;;;AAGA;;;;;;AAKA;;;;;AAIA;;;;;;;AAMA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAQA;;;;;AAOA;;;;AAgBA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAUA;;;;;AAQA;;;;AAOA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA", "debugId": null}}]}