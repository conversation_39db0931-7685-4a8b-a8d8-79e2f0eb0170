{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.module.css"], "sourcesContent": ["/* cyrillic-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* greek-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+1F00-1FFF;\n}\n/* greek */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Inter Fallback';\n    src: local(\"Arial\");\n    ascent-override: 90.44%;\ndescent-override: 22.52%;\nline-gap-override: 0.00%;\nsize-adjust: 107.12%;\n\n}\n.className {\n    font-family: 'Inter', 'Inter Fallback';\n    font-style: normal;\n\n}\n.variable {\n    --font-inter: 'Inter', 'Inter Fallback';\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/TodoCalender/task-finance-tracker/src/app/globals.css"], "sourcesContent": ["/* ===== RESET & BASE STYLES ===== */\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nhtml, body {\n  height: 100%;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Open Sans', 'Helvetica Neue', sans-serif;\n  line-height: 1.6;\n  color: #2d3748;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  min-height: 100vh;\n}\n\n/* ===== LAYOUT ===== */\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 1rem;\n}\n\n.flex {\n  display: flex;\n}\n\n.flex-col {\n  flex-direction: column;\n}\n\n.items-center {\n  align-items: center;\n}\n\n.justify-center {\n  justify-content: center;\n}\n\n.justify-between {\n  justify-content: space-between;\n}\n\n.space-x-4 > * + * {\n  margin-left: 1rem;\n}\n\n.space-y-4 > * + * {\n  margin-top: 1rem;\n}\n\n.space-y-6 > * + * {\n  margin-top: 1.5rem;\n}\n\n.grid {\n  display: grid;\n}\n\n.grid-cols-1 {\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\n\n.grid-cols-2 {\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n}\n\n.grid-cols-3 {\n  grid-template-columns: repeat(3, minmax(0, 1fr));\n}\n\n.gap-4 {\n  gap: 1rem;\n}\n\n.gap-6 {\n  gap: 1.5rem;\n}\n\n/* ===== CARDS ===== */\n.card {\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(20px);\n          backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 16px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  padding: 1.5rem;\n  transition: all 0.3s ease;\n}\n\n.card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\n}\n\n/* ===== BUTTONS ===== */\n.btn {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 12px;\n  font-size: 0.875rem;\n  font-weight: 600;\n  text-decoration: none;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.btn:before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  transition: left 0.5s;\n}\n\n.btn:hover:before {\n  left: 100%;\n}\n\n.btn-primary {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);\n}\n\n.btn-primary:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);\n}\n\n.btn-secondary {\n  background: rgba(255, 255, 255, 0.9);\n  color: #4a5568;\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.btn-secondary:hover {\n  background: white;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none !important;\n}\n\n.btn-sm {\n  padding: 0.5rem 1rem;\n  font-size: 0.75rem;\n}\n\n.btn-lg {\n  padding: 1rem 2rem;\n  font-size: 1rem;\n}\n\n/* ===== FORMS ===== */\n.form-input {\n  width: 100%;\n  padding: 0.75rem 1rem;\n  background: rgba(255, 255, 255, 0.9);\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-radius: 12px;\n  font-size: 0.875rem;\n  transition: all 0.3s ease;\n  -webkit-backdrop-filter: blur(10px);\n          backdrop-filter: blur(10px);\n}\n\n.form-input:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n  transform: translateY(-1px);\n}\n\n.form-input::-moz-placeholder {\n  color: #a0aec0;\n}\n\n.form-input::placeholder {\n  color: #a0aec0;\n}\n\n.form-label {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-size: 0.875rem;\n  font-weight: 600;\n  color: #4a5568;\n}\n\n.form-group {\n  margin-bottom: 1.5rem;\n}\n\n/* ===== NAVIGATION ===== */\n.navbar {\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(20px);\n          backdrop-filter: blur(20px);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n  padding: 1rem 0;\n}\n\n.nav-link {\n  color: #4a5568;\n  text-decoration: none;\n  padding: 0.5rem 1rem;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n  font-weight: 500;\n}\n\n.nav-link:hover {\n  background: rgba(102, 126, 234, 0.1);\n  color: #667eea;\n}\n\n.nav-link.active {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n/* ===== TYPOGRAPHY ===== */\nh1 {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #2d3748;\n  margin-bottom: 0.5rem;\n}\n\nh2 {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #2d3748;\n  margin-bottom: 1rem;\n}\n\nh3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #2d3748;\n  margin-bottom: 0.75rem;\n}\n\np {\n  color: #718096;\n  margin-bottom: 1rem;\n}\n\n/* ===== UTILITIES ===== */\n.text-center {\n  text-align: center;\n}\n\n.text-white {\n  color: white;\n}\n\n.text-gray-600 {\n  color: #718096;\n}\n\n.text-gray-900 {\n  color: #2d3748;\n}\n\n.bg-white {\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(20px);\n          backdrop-filter: blur(20px);\n}\n\n.shadow {\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n}\n\n.shadow-lg {\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\n}\n\n.shadow-xl {\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);\n}\n\n.rounded {\n  border-radius: 8px;\n}\n\n.rounded-lg {\n  border-radius: 12px;\n}\n\n.rounded-xl {\n  border-radius: 16px;\n}\n\n.p-4 {\n  padding: 1rem;\n}\n\n.p-6 {\n  padding: 1.5rem;\n}\n\n.p-8 {\n  padding: 2rem;\n}\n\n.px-4 {\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\n\n.py-2 {\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\n\n.py-4 {\n  padding-top: 1rem;\n  padding-bottom: 1rem;\n}\n\n.py-8 {\n  padding-top: 2rem;\n  padding-bottom: 2rem;\n}\n\n.mb-4 {\n  margin-bottom: 1rem;\n}\n\n.mb-6 {\n  margin-bottom: 1.5rem;\n}\n\n.mb-8 {\n  margin-bottom: 2rem;\n}\n\n.mt-8 {\n  margin-top: 2rem;\n}\n\n.w-full {\n  width: 100%;\n}\n\n.max-w-md {\n  max-width: 28rem;\n}\n\n.max-w-7xl {\n  max-width: 80rem;\n}\n\n.min-h-screen {\n  min-height: 100vh;\n}\n\n.hidden {\n  display: none;\n}\n\n.block {\n  display: block;\n}\n\n.inline-flex {\n  display: inline-flex;\n}\n\n/* ===== RESPONSIVE ===== */\n@media (min-width: 640px) {\n  .sm\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .sm\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .sm\\:px-6 {\n    padding-left: 1.5rem;\n    padding-right: 1.5rem;\n  }\n\n  .sm\\:block {\n    display: block;\n  }\n\n  .sm\\:hidden {\n    display: none;\n  }\n}\n\n@media (min-width: 1024px) {\n  .lg\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .lg\\:px-8 {\n    padding-left: 2rem;\n    padding-right: 2rem;\n  }\n}\n\n/* ===== COMPONENT SPECIFIC ===== */\n.task-item {\n  background: rgba(255, 255, 255, 0.9);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin-bottom: 1rem;\n  transition: all 0.3s ease;\n  -webkit-backdrop-filter: blur(10px);\n          backdrop-filter: blur(10px);\n}\n\n.task-item:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n}\n\n.task-item.completed {\n  opacity: 0.7;\n}\n\n.task-item.completed .task-title {\n  text-decoration: line-through;\n  color: #a0aec0;\n}\n\n.task-title {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #2d3748;\n  margin-bottom: 0.5rem;\n}\n\n.task-description {\n  color: #718096;\n  margin-bottom: 1rem;\n}\n\n.status-badge {\n  display: inline-block;\n  padding: 0.25rem 0.75rem;\n  border-radius: 20px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n\n.status-not-started {\n  background: rgba(160, 174, 192, 0.2);\n  color: #4a5568;\n}\n\n.status-in-progress {\n  background: rgba(102, 126, 234, 0.2);\n  color: #667eea;\n}\n\n.status-completed {\n  background: rgba(72, 187, 120, 0.2);\n  color: #48bb78;\n}\n\n.priority-high {\n  background: rgba(245, 101, 101, 0.2);\n  color: #f56565;\n}\n\n.priority-medium {\n  background: rgba(237, 137, 54, 0.2);\n  color: #ed8936;\n}\n\n.priority-low {\n  background: rgba(160, 174, 192, 0.2);\n  color: #a0aec0;\n}\n\n.dialog-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  -webkit-backdrop-filter: blur(4px);\n          backdrop-filter: blur(4px);\n  z-index: 50;\n}\n\n.dialog-content {\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(20px);\n          backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 20px;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n  max-width: 90vw;\n  max-height: 90vh;\n  overflow-y: auto;\n  z-index: 51;\n}\n\n/* ===== ANIMATIONS ===== */\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.fade-in {\n  animation: fadeIn 0.5s ease-out;\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateX(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n.slide-in {\n  animation: slideIn 0.3s ease-out;\n}\n\n\n"], "names": [], "mappings": "AACA;;;;;;AAMA;;;;;;;;;AAUA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;;;;;;AAWA;;;;;AAMA;;;;;;;;;;;;;;;;AAgBA;;;;;;;;;;;AAWA;;;;AAIA;;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;AAMA;;;;;;;;;;;AAYA;;;;;;;AAOA;;;;AAAA;;;;AAQA;;;;;;;;AAQA;;;;AAKA;;;;;;;;AASA;;;;;;;;;AASA;;;;;AAKA;;;;;AAMA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;;;AAOF;;;;;;;;;;AAWA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;;;AAOA;;;;;AAKA;;;;;;;;;;AAUA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;;;AAYA;;;;;;;;;;;;;;;;AAkBA;;;;;;;;;;;;AAWA;;;;AAIA;;;;;;;;;;;;AAWA", "debugId": null}}]}