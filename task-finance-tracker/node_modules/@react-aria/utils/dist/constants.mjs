/*
 * Copyright 2024 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ // Custom event names for updating the autocomplete's aria-activedecendant.
const $5671b20cf9b562b2$export$447a38995de2c711 = 'react-aria-clear-focus';
const $5671b20cf9b562b2$export$831c820ad60f9d12 = 'react-aria-focus';


export {$5671b20cf9b562b2$export$447a38995de2c711 as CLEAR_FOCUS_EVENT, $5671b20cf9b562b2$export$831c820ad60f9d12 as FOCUS_EVENT};
//# sourceMappingURL=constants.module.js.map
