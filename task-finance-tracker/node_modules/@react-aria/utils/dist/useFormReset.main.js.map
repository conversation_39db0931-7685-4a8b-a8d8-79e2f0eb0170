{"mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC;;AAMM,SAAS,0CACd,GAA6F,EAC7F,YAAe,EACf,OAA2B;IAE3B,IAAI,aAAa,CAAA,GAAA,mBAAK,EAAE;IACxB,IAAI,cAAc,CAAA,GAAA,wCAAa,EAAE;QAC/B,IAAI,SACF,QAAQ,WAAW,OAAO;IAE9B;IAEA,CAAA,GAAA,sBAAQ,EAAE;YACG;QAAX,IAAI,OAAO,gBAAA,2BAAA,eAAA,IAAK,OAAO,cAAZ,mCAAA,aAAc,IAAI;QAC7B,iBAAA,2BAAA,KAAM,gBAAgB,CAAC,SAAS;QAChC,OAAO;YACL,iBAAA,2BAAA,KAAM,mBAAmB,CAAC,SAAS;QACrC;IACF,GAAG;QAAC;QAAK;KAAY;AACvB", "sources": ["packages/@react-aria/utils/src/useFormReset.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {RefObject} from '@react-types/shared';\nimport {useEffect, useRef} from 'react';\nimport {useEffectEvent} from './useEffectEvent';\n\nexport function useFormReset<T>(\n  ref: RefObject<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement | null> | undefined,\n  initialValue: T,\n  onReset: (value: T) => void\n): void {\n  let resetValue = useRef(initialValue);\n  let handleReset = useEffectEvent(() => {\n    if (onReset) {\n      onReset(resetValue.current);\n    }\n  });\n\n  useEffect(() => {\n    let form = ref?.current?.form;\n    form?.addEventListener('reset', handleReset);\n    return () => {\n      form?.removeEventListener('reset', handleReset);\n    };\n  }, [ref, handleReset]);\n}\n"], "names": [], "version": 3, "file": "useFormReset.main.js.map"}