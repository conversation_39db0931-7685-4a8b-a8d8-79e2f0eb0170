{"mappings": ";;;AAAA;;;;;;;;;;CAUC;;AAKM,SAAS,0CAAmC,EAAM;IACvD,MAAM,MAAM,CAAA,GAAA,aAAK,EAAwB;IACzC,CAAA,GAAA,yCAAc,EAAE;QACd,IAAI,OAAO,GAAG;IAChB,GAAG;QAAC;KAAG;IACP,aAAa;IACb,OAAO,CAAA,GAAA,kBAAU,EAAK,CAAC,GAAG;QACxB,MAAM,IAAI,IAAI,OAAO;QACrB,OAAO,cAAA,wBAAA,KAAO;IAChB,GAAG,EAAE;AACP", "sources": ["packages/@react-aria/utils/src/useEffectEvent.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useRef} from 'react';\nimport {useLayoutEffect} from './useLayoutEffect';\n\nexport function useEffectEvent<T extends Function>(fn?: T): T {\n  const ref = useRef<T | null | undefined>(null);\n  useLayoutEffect(() => {\n    ref.current = fn;\n  }, [fn]);\n  // @ts-ignore\n  return useCallback<T>((...args) => {\n    const f = ref.current!;\n    return f?.(...args);\n  }, []);\n}\n"], "names": [], "version": 3, "file": "useEffectEvent.module.js.map"}