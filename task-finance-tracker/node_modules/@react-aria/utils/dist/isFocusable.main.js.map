{"mappings": ";;;;;;;AAAA,MAAM,0CAAoB;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,mDAA6B,wCAAkB,IAAI,CAAC,qBAAqB;AAE/E,wCAAkB,IAAI,CAAC;AACvB,MAAM,kDAA4B,wCAAkB,IAAI,CAAC;AAElD,SAAS,0CAAY,OAAgB;IAC1C,OAAO,QAAQ,OAAO,CAAC;AACzB;AAEO,SAAS,0CAAW,OAAgB;IACzC,OAAO,QAAQ,OAAO,CAAC;AACzB", "sources": ["packages/@react-aria/utils/src/isFocusable.ts"], "sourcesContent": ["const focusableElements = [\n  'input:not([disabled]):not([type=hidden])',\n  'select:not([disabled])',\n  'textarea:not([disabled])',\n  'button:not([disabled])',\n  'a[href]',\n  'area[href]',\n  'summary',\n  'iframe',\n  'object',\n  'embed',\n  'audio[controls]',\n  'video[controls]',\n  '[contenteditable]:not([contenteditable^=\"false\"])'\n];\n\nconst FOCUSABLE_ELEMENT_SELECTOR = focusableElements.join(':not([hidden]),') + ',[tabindex]:not([disabled]):not([hidden])';\n\nfocusableElements.push('[tabindex]:not([tabindex=\"-1\"]):not([disabled])');\nconst TABBABLE_ELEMENT_SELECTOR = focusableElements.join(':not([hidden]):not([tabindex=\"-1\"]),');\n\nexport function isFocusable(element: Element): boolean {\n  return element.matches(FOCUSABLE_ELEMENT_SELECTOR);\n}\n\nexport function isTabbable(element: Element): boolean {\n  return element.matches(TABBABLE_ELEMENT_SELECTOR);\n}\n"], "names": [], "version": 3, "file": "isFocusable.main.js.map"}