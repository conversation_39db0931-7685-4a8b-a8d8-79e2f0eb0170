{"mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC;;AAUD,IAAI,uCAAiB,OAAO,aAAa,eAAe,OAAO,cAAc;AAEtE,SAAS;IACd,IAAI,QAAQ,CAAA,GAAA,4BAAO;IACnB,IAAI,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qBAAO,EAAE,IAAM,QAAQ;YAAC,OAAO;YAAG,QAAQ;QAAC,IAAI;IAErE,CAAA,GAAA,sBAAQ,EAAE;QACR,wFAAwF;QACxF,IAAI,WAAW;YACb,QAAQ,CAAA;gBACN,IAAI,UAAU;gBACd,IAAI,QAAQ,KAAK,KAAK,KAAK,KAAK,IAAI,QAAQ,MAAM,KAAK,KAAK,MAAM,EAChE,OAAO;gBAET,OAAO;YACT;QACF;QAEA,IAAI,CAAC,sCACH,OAAO,gBAAgB,CAAC,UAAU;aAElC,qCAAe,gBAAgB,CAAC,UAAU;QAG5C,OAAO;YACL,IAAI,CAAC,sCACH,OAAO,mBAAmB,CAAC,UAAU;iBAErC,qCAAe,mBAAmB,CAAC,UAAU;QAEjD;IACF,GAAG,EAAE;IAEL,OAAO;AACT;AAEA,SAAS;IACP,OAAO;QACL,OAAO,AAAC,yCAAkB,iDAAA,2DAAA,qCAAgB,KAAK,KAAK,OAAO,UAAU;QACrE,QAAQ,AAAC,yCAAkB,iDAAA,2DAAA,qCAAgB,MAAM,KAAK,OAAO,WAAW;IAC1E;AACF", "sources": ["packages/@react-aria/utils/src/useViewportSize.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useEffect, useState} from 'react';\nimport {useIsSSR} from '@react-aria/ssr';\n\ninterface ViewportSize {\n  width: number,\n  height: number\n}\n\nlet visualViewport = typeof document !== 'undefined' && window.visualViewport;\n\nexport function useViewportSize(): ViewportSize {\n  let isSSR = useIsSSR();\n  let [size, setSize] = useState(() => isSSR ? {width: 0, height: 0} : getViewportSize());\n\n  useEffect(() => {\n    // Use visualViewport api to track available height even on iOS virtual keyboard opening\n    let onResize = () => {\n      setSize(size => {\n        let newSize = getViewportSize();\n        if (newSize.width === size.width && newSize.height === size.height) {\n          return size;\n        }\n        return newSize;\n      });\n    };\n\n    if (!visualViewport) {\n      window.addEventListener('resize', onResize);\n    } else {\n      visualViewport.addEventListener('resize', onResize);\n    }\n\n    return () => {\n      if (!visualViewport) {\n        window.removeEventListener('resize', onResize);\n      } else {\n        visualViewport.removeEventListener('resize', onResize);\n      }\n    };\n  }, []);\n\n  return size;\n}\n\nfunction getViewportSize(): ViewportSize {\n  return {\n    width: (visualViewport && visualViewport?.width) || window.innerWidth,\n    height: (visualViewport && visualViewport?.height) || window.innerHeight\n  };\n}\n"], "names": [], "version": 3, "file": "useViewportSize.main.js.map"}