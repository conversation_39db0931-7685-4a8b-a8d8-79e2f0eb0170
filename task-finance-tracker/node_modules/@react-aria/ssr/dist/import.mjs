import {SSRProvider as $b5e257d569688ac6$export$9f8ac96af4b1b2ae, useIsSSR as $b5e257d569688ac6$export$535bd6ca7f90a273, useSSRSafeId as $b5e257d569688ac6$export$619500959fc48b26} from "./SSRProvider.mjs";

/*
 * Copyright 2020 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 


export {$b5e257d569688ac6$export$9f8ac96af4b1b2ae as SSR<PERSON>rovider, $b5e257d569688ac6$export$619500959fc48b26 as useSSRSafeId, $b5e257d569688ac6$export$535bd6ca7f90a273 as useIsSSR};
//# sourceMappingURL=module.js.map
