{"version": 3, "file": "utils.js", "sources": ["../../src/utils.ts"], "sourcesContent": ["export type NoInfer<A extends any> = [A][A extends any ? 0 : never]\n\nexport type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>\n\nexport function memo<TDeps extends ReadonlyArray<any>, TResult>(\n  getDeps: () => [...TDeps],\n  fn: (...args: NoInfer<[...TDeps]>) => TResult,\n  opts: {\n    key: false | string\n    debug?: () => boolean\n    onChange?: (result: TResult) => void\n    initialDeps?: TDeps\n  },\n) {\n  let deps = opts.initialDeps ?? []\n  let result: TResult | undefined\n\n  function memoizedFunction(): TResult {\n    let depTime: number\n    if (opts.key && opts.debug?.()) depTime = Date.now()\n\n    const newDeps = getDeps()\n\n    const depsChanged =\n      newDeps.length !== deps.length ||\n      newDeps.some((dep: any, index: number) => deps[index] !== dep)\n\n    if (!depsChanged) {\n      return result!\n    }\n\n    deps = newDeps\n\n    let resultTime: number\n    if (opts.key && opts.debug?.()) resultTime = Date.now()\n\n    result = fn(...newDeps)\n\n    if (opts.key && opts.debug?.()) {\n      const depEndTime = Math.round((Date.now() - depTime!) * 100) / 100\n      const resultEndTime = Math.round((Date.now() - resultTime!) * 100) / 100\n      const resultFpsPercentage = resultEndTime / 16\n\n      const pad = (str: number | string, num: number) => {\n        str = String(str)\n        while (str.length < num) {\n          str = ' ' + str\n        }\n        return str\n      }\n\n      console.info(\n        `%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`,\n        `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(\n              0,\n              Math.min(120 - 120 * resultFpsPercentage, 120),\n            )}deg 100% 31%);`,\n        opts?.key,\n      )\n    }\n\n    opts?.onChange?.(result)\n\n    return result\n  }\n\n  // Attach updateDeps to the function itself\n  memoizedFunction.updateDeps = (newDeps: [...TDeps]) => {\n    deps = newDeps\n  }\n\n  return memoizedFunction\n}\n\nexport function notUndefined<T>(value: T | undefined, msg?: string): T {\n  if (value === undefined) {\n    throw new Error(`Unexpected undefined${msg ? `: ${msg}` : ''}`)\n  } else {\n    return value\n  }\n}\n\nexport const approxEqual = (a: number, b: number) => Math.abs(a - b) <= 1\n\nexport const debounce = (\n  targetWindow: Window & typeof globalThis,\n  fn: Function,\n  ms: number,\n) => {\n  let timeoutId: number\n  return function (this: any, ...args: Array<any>) {\n    targetWindow.clearTimeout(timeoutId)\n    timeoutId = targetWindow.setTimeout(() => fn.apply(this, args), ms)\n  }\n}\n"], "names": [], "mappings": "AAIgB,SAAA,KACd,SACA,IACA,MAMA;AACI,MAAA,OAAO,KAAK,eAAe,CAAC;AAC5B,MAAA;AAEJ,WAAS,mBAA4B;AAbvB;AAcR,QAAA;AACJ,QAAI,KAAK,SAAO,UAAK,UAAL,+BAAgB,WAAU,KAAK,IAAI;AAEnD,UAAM,UAAU,QAAQ;AAExB,UAAM,cACJ,QAAQ,WAAW,KAAK,UACxB,QAAQ,KAAK,CAAC,KAAU,UAAkB,KAAK,KAAK,MAAM,GAAG;AAE/D,QAAI,CAAC,aAAa;AACT,aAAA;AAAA,IAAA;AAGF,WAAA;AAEH,QAAA;AACJ,QAAI,KAAK,SAAO,UAAK,UAAL,+BAAgB,cAAa,KAAK,IAAI;AAE7C,aAAA,GAAG,GAAG,OAAO;AAEtB,QAAI,KAAK,SAAO,UAAK,UAAL,gCAAgB;AACxB,YAAA,aAAa,KAAK,OAAO,KAAK,QAAQ,WAAY,GAAG,IAAI;AACzD,YAAA,gBAAgB,KAAK,OAAO,KAAK,QAAQ,cAAe,GAAG,IAAI;AACrE,YAAM,sBAAsB,gBAAgB;AAEtC,YAAA,MAAM,CAAC,KAAsB,QAAgB;AACjD,cAAM,OAAO,GAAG;AACT,eAAA,IAAI,SAAS,KAAK;AACvB,gBAAM,MAAM;AAAA,QAAA;AAEP,eAAA;AAAA,MACT;AAEQ,cAAA;AAAA,QACN,OAAO,IAAI,eAAe,CAAC,CAAC,KAAK,IAAI,YAAY,CAAC,CAAC;AAAA,QACnD;AAAA;AAAA;AAAA,yBAGiB,KAAK;AAAA,UAChB;AAAA,UACA,KAAK,IAAI,MAAM,MAAM,qBAAqB,GAAG;AAAA,QAC9C,CAAA;AAAA,QACL,6BAAM;AAAA,MACR;AAAA,IAAA;AAGF,uCAAM,aAAN,8BAAiB;AAEV,WAAA;AAAA,EAAA;AAIQ,mBAAA,aAAa,CAAC,YAAwB;AAC9C,WAAA;AAAA,EACT;AAEO,SAAA;AACT;AAEgB,SAAA,aAAgB,OAAsB,KAAiB;AACrE,MAAI,UAAU,QAAW;AACjB,UAAA,IAAI,MAAM,uBAAuB,MAAM,KAAK,GAAG,KAAK,EAAE,EAAE;AAAA,EAAA,OACzD;AACE,WAAA;AAAA,EAAA;AAEX;AAEa,MAAA,cAAc,CAAC,GAAW,MAAc,KAAK,IAAI,IAAI,CAAC,KAAK;AAEjE,MAAM,WAAW,CACtB,cACA,IACA,OACG;AACC,MAAA;AACJ,SAAO,YAAwB,MAAkB;AAC/C,iBAAa,aAAa,SAAS;AACvB,gBAAA,aAAa,WAAW,MAAM,GAAG,MAAM,MAAM,IAAI,GAAG,EAAE;AAAA,EACpE;AACF;"}