{"name": "@vercel/kv", "version": "3.0.0", "description": "Durable <PERSON>", "homepage": "https://vercel.com", "repository": {"type": "git", "url": "https://github.com/vercel/storage.git", "directory": "packages/kv"}, "license": "Apache-2.0", "sideEffects": false, "type": "module", "exports": {".": {"import": {"node": "./dist/index.js", "default": "./dist/index.js"}, "require": {"node": "./dist/index.cjs", "default": "./dist/index.cjs"}}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "files": ["dist"], "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "dependencies": {"@upstash/redis": "^1.34.0"}, "devDependencies": {"@changesets/cli": "2.27.8", "@edge-runtime/jest-environment": "2.3.10", "@edge-runtime/types": "2.2.9", "@types/jest": "29.5.13", "@types/node": "22.6.1", "eslint": "8.56.0", "husky": "9.1.6", "jest": "29.7.0", "lint-staged": "15.2.10", "prettier": "3.3.3", "ts-jest": "29.2.5", "tsup": "8.3.0", "typescript": "5.6.2", "eslint-config-custom": "0.0.0", "tsconfig": "0.0.0"}, "engines": {"node": ">=14.6"}, "scripts": {"build": "tsup", "dev": "tsup --watch --clean=false", "lint": "eslint --max-warnings=0 .", "prettier-check": "prettier --check .", "publint": "npx publint", "test": "jest --env @edge-runtime/jest-environment .test.ts && jest --env node .test.ts", "type-check": "tsc --noEmit"}}