{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["import { Redis } from '@upstash/redis';\nimport type { ScanCommandOptions, RedisConfigNodejs } from '@upstash/redis';\n\nlet _kv: Redis | null = null;\nprocess.env.UPSTASH_DISABLE_TELEMETRY = '1';\n\nexport class VercelKV extends Redis {\n  // This API is based on https://github.com/redis/node-redis#scan-iterator which is not supported in @upstash/redis\n  /**\n   * Same as `scan` but returns an AsyncIterator to allow iteration via `for await`.\n   */\n  async *scanIterator(options?: ScanCommandOptions): AsyncIterable<string> {\n    let cursor = '0';\n    let keys: string[];\n    do {\n      // eslint-disable-next-line no-await-in-loop -- [@vercel/style-guide@5 migration]\n      [cursor, keys] = await this.scan(cursor, options);\n      for (const key of keys) {\n        yield key;\n      }\n    } while (cursor !== '0');\n  }\n\n  /**\n   * Same as `hscan` but returns an AsyncIterator to allow iteration via `for await`.\n   */\n  async *hscanIterator(\n    key: string,\n    options?: ScanCommandOptions,\n  ): AsyncIterable<string | number> {\n    let cursor = '0';\n    let items: (number | string)[];\n    do {\n      // eslint-disable-next-line no-await-in-loop -- [@vercel/style-guide@5 migration]\n      [cursor, items] = await this.hscan(key, cursor, options);\n      for (const item of items) {\n        yield item;\n      }\n    } while (cursor !== '0');\n  }\n\n  /**\n   * Same as `sscan` but returns an AsyncIterator to allow iteration via `for await`.\n   */\n  async *sscanIterator(\n    key: string,\n    options?: ScanCommandOptions,\n  ): AsyncIterable<string | number> {\n    let cursor = '0';\n    let items: (number | string)[];\n    do {\n      // eslint-disable-next-line no-await-in-loop -- [@vercel/style-guide@5 migration]\n      [cursor, items] = await this.sscan(key, cursor, options);\n      for (const item of items) {\n        yield item;\n      }\n    } while (cursor !== '0');\n  }\n\n  /**\n   * Same as `zscan` but returns an AsyncIterator to allow iteration via `for await`.\n   */\n  async *zscanIterator(\n    key: string,\n    options?: ScanCommandOptions,\n  ): AsyncIterable<string | number> {\n    let cursor = '0';\n    let items: (number | string)[];\n    do {\n      // eslint-disable-next-line no-await-in-loop -- [@vercel/style-guide@5 migration]\n      [cursor, items] = await this.zscan(key, cursor, options);\n      for (const item of items) {\n        yield item;\n      }\n    } while (cursor !== '0');\n  }\n}\n\nexport function createClient(config: RedisConfigNodejs): VercelKV {\n  return new VercelKV({\n    // The Next.js team recommends no value or `default` for fetch requests's `cache` option\n    // upstash/redis defaults to `no-store`, so we enforce `default`\n    cache: 'default',\n    enableAutoPipelining: true,\n    ...config,\n  });\n}\n\n// eslint-disable-next-line import/no-default-export -- [@vercel/style-guide@5 migration]\nexport default new Proxy(\n  {},\n  {\n    get(target, prop, receiver) {\n      if (prop === 'then' || prop === 'parse') {\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-return -- [@vercel/style-guide@5 migration]\n        return Reflect.get(target, prop, receiver);\n      }\n\n      if (!_kv) {\n        if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {\n          throw new Error(\n            '@vercel/kv: Missing required environment variables KV_REST_API_URL and KV_REST_API_TOKEN',\n          );\n        }\n        // eslint-disable-next-line no-console -- [@vercel/style-guide@5 migration]\n        console.warn(\n          '\\x1b[33m\"The default export has been moved to a named export and it will be removed in version 1, change to import { kv }\\x1b[0m\"',\n        );\n\n        _kv = createClient({\n          url: process.env.KV_REST_API_URL,\n          token: process.env.KV_REST_API_TOKEN,\n        });\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-return -- [@vercel/style-guide@5 migration]\n      return Reflect.get(_kv, prop);\n    },\n  },\n) as VercelKV;\n\nexport const kv = new Proxy(\n  {},\n  {\n    get(target, prop) {\n      if (!_kv) {\n        if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {\n          throw new Error(\n            '@vercel/kv: Missing required environment variables KV_REST_API_URL and KV_REST_API_TOKEN',\n          );\n        }\n\n        _kv = createClient({\n          url: process.env.KV_REST_API_URL,\n          token: process.env.KV_REST_API_TOKEN,\n        });\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-return -- [@vercel/style-guide@5 migration]\n      return Reflect.get(_kv, prop);\n    },\n  },\n) as VercelKV;\n"], "mappings": ";AAAA,SAAS,aAAa;AAGtB,IAAI,MAAoB;AACxB,QAAQ,IAAI,4BAA4B;AAEjC,IAAM,WAAN,cAAuB,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlC,OAAO,aAAa,SAAqD;AACvE,QAAI,SAAS;AACb,QAAI;AACJ,OAAG;AAED,OAAC,QAAQ,IAAI,IAAI,MAAM,KAAK,KAAK,QAAQ,OAAO;AAChD,iBAAW,OAAO,MAAM;AACtB,cAAM;AAAA,MACR;AAAA,IACF,SAAS,WAAW;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,cACL,KACA,SACgC;AAChC,QAAI,SAAS;AACb,QAAI;AACJ,OAAG;AAED,OAAC,QAAQ,KAAK,IAAI,MAAM,KAAK,MAAM,KAAK,QAAQ,OAAO;AACvD,iBAAW,QAAQ,OAAO;AACxB,cAAM;AAAA,MACR;AAAA,IACF,SAAS,WAAW;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,cACL,KACA,SACgC;AAChC,QAAI,SAAS;AACb,QAAI;AACJ,OAAG;AAED,OAAC,QAAQ,KAAK,IAAI,MAAM,KAAK,MAAM,KAAK,QAAQ,OAAO;AACvD,iBAAW,QAAQ,OAAO;AACxB,cAAM;AAAA,MACR;AAAA,IACF,SAAS,WAAW;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,cACL,KACA,SACgC;AAChC,QAAI,SAAS;AACb,QAAI;AACJ,OAAG;AAED,OAAC,QAAQ,KAAK,IAAI,MAAM,KAAK,MAAM,KAAK,QAAQ,OAAO;AACvD,iBAAW,QAAQ,OAAO;AACxB,cAAM;AAAA,MACR;AAAA,IACF,SAAS,WAAW;AAAA,EACtB;AACF;AAEO,SAAS,aAAa,QAAqC;AAChE,SAAO,IAAI,SAAS;AAAA;AAAA;AAAA,IAGlB,OAAO;AAAA,IACP,sBAAsB;AAAA,IACtB,GAAG;AAAA,EACL,CAAC;AACH;AAGA,IAAO,cAAQ,IAAI;AAAA,EACjB,CAAC;AAAA,EACD;AAAA,IACE,IAAI,QAAQ,MAAM,UAAU;AAC1B,UAAI,SAAS,UAAU,SAAS,SAAS;AAEvC,eAAO,QAAQ,IAAI,QAAQ,MAAM,QAAQ;AAAA,MAC3C;AAEA,UAAI,CAAC,KAAK;AACR,YAAI,CAAC,QAAQ,IAAI,mBAAmB,CAAC,QAAQ,IAAI,mBAAmB;AAClE,gBAAM,IAAI;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAEA,gBAAQ;AAAA,UACN;AAAA,QACF;AAEA,cAAM,aAAa;AAAA,UACjB,KAAK,QAAQ,IAAI;AAAA,UACjB,OAAO,QAAQ,IAAI;AAAA,QACrB,CAAC;AAAA,MACH;AAGA,aAAO,QAAQ,IAAI,KAAK,IAAI;AAAA,IAC9B;AAAA,EACF;AACF;AAEO,IAAM,KAAK,IAAI;AAAA,EACpB,CAAC;AAAA,EACD;AAAA,IACE,IAAI,QAAQ,MAAM;AAChB,UAAI,CAAC,KAAK;AACR,YAAI,CAAC,QAAQ,IAAI,mBAAmB,CAAC,QAAQ,IAAI,mBAAmB;AAClE,gBAAM,IAAI;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAEA,cAAM,aAAa;AAAA,UACjB,KAAK,QAAQ,IAAI;AAAA,UACjB,OAAO,QAAQ,IAAI;AAAA,QACrB,CAAC;AAAA,MACH;AAGA,aAAO,QAAQ,IAAI,KAAK,IAAI;AAAA,IAC9B;AAAA,EACF;AACF;", "names": []}