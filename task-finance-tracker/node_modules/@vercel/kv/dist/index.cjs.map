{"version": 3, "sources": ["/home/<USER>/work/storage/storage/packages/kv/dist/index.cjs", "../src/index.ts"], "names": [], "mappings": "AAAA;ACAA,uCAAsB;AAGtB,IAAI,IAAA,EAAoB,IAAA;AACxB,OAAA,CAAQ,GAAA,CAAI,0BAAA,EAA4B,GAAA;AAEjC,IAAM,SAAA,EAAN,MAAA,QAAuB,aAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlC,MAAA,CAAO,YAAA,CAAa,OAAA,EAAqD;AACvE,IAAA,IAAI,OAAA,EAAS,GAAA;AACb,IAAA,IAAI,IAAA;AACJ,IAAA,GAAG;AAED,MAAA,CAAC,MAAA,EAAQ,IAAI,EAAA,EAAI,MAAM,IAAA,CAAK,IAAA,CAAK,MAAA,EAAQ,OAAO,CAAA;AAChD,MAAA,IAAA,CAAA,MAAW,IAAA,GAAO,IAAA,EAAM;AACtB,QAAA,MAAM,GAAA;AAAA,MACR;AAAA,IACF,EAAA,MAAA,CAAS,OAAA,IAAW,GAAA,CAAA;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAA,CAAO,aAAA,CACL,GAAA,EACA,OAAA,EACgC;AAChC,IAAA,IAAI,OAAA,EAAS,GAAA;AACb,IAAA,IAAI,KAAA;AACJ,IAAA,GAAG;AAED,MAAA,CAAC,MAAA,EAAQ,KAAK,EAAA,EAAI,MAAM,IAAA,CAAK,KAAA,CAAM,GAAA,EAAK,MAAA,EAAQ,OAAO,CAAA;AACvD,MAAA,IAAA,CAAA,MAAW,KAAA,GAAQ,KAAA,EAAO;AACxB,QAAA,MAAM,IAAA;AAAA,MACR;AAAA,IACF,EAAA,MAAA,CAAS,OAAA,IAAW,GAAA,CAAA;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAA,CAAO,aAAA,CACL,GAAA,EACA,OAAA,EACgC;AAChC,IAAA,IAAI,OAAA,EAAS,GAAA;AACb,IAAA,IAAI,KAAA;AACJ,IAAA,GAAG;AAED,MAAA,CAAC,MAAA,EAAQ,KAAK,EAAA,EAAI,MAAM,IAAA,CAAK,KAAA,CAAM,GAAA,EAAK,MAAA,EAAQ,OAAO,CAAA;AACvD,MAAA,IAAA,CAAA,MAAW,KAAA,GAAQ,KAAA,EAAO;AACxB,QAAA,MAAM,IAAA;AAAA,MACR;AAAA,IACF,EAAA,MAAA,CAAS,OAAA,IAAW,GAAA,CAAA;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAA,CAAO,aAAA,CACL,GAAA,EACA,OAAA,EACgC;AAChC,IAAA,IAAI,OAAA,EAAS,GAAA;AACb,IAAA,IAAI,KAAA;AACJ,IAAA,GAAG;AAED,MAAA,CAAC,MAAA,EAAQ,KAAK,EAAA,EAAI,MAAM,IAAA,CAAK,KAAA,CAAM,GAAA,EAAK,MAAA,EAAQ,OAAO,CAAA;AACvD,MAAA,IAAA,CAAA,MAAW,KAAA,GAAQ,KAAA,EAAO;AACxB,QAAA,MAAM,IAAA;AAAA,MACR;AAAA,IACF,EAAA,MAAA,CAAS,OAAA,IAAW,GAAA,CAAA;AAAA,EACtB;AACF,CAAA;AAEO,SAAS,YAAA,CAAa,MAAA,EAAqC;AAChE,EAAA,OAAO,IAAI,QAAA,CAAS;AAAA;AAAA;AAAA,IAGlB,KAAA,EAAO,SAAA;AAAA,IACP,oBAAA,EAAsB,IAAA;AAAA,IACtB,GAAG;AAAA,EACL,CAAC,CAAA;AACH;AAGA,IAAO,YAAA,EAAQ,IAAI,KAAA;AAAA,EACjB,CAAC,CAAA;AAAA,EACD;AAAA,IACE,GAAA,CAAI,MAAA,EAAQ,IAAA,EAAM,QAAA,EAAU;AAC1B,MAAA,GAAA,CAAI,KAAA,IAAS,OAAA,GAAU,KAAA,IAAS,OAAA,EAAS;AAEvC,QAAA,OAAO,OAAA,CAAQ,GAAA,CAAI,MAAA,EAAQ,IAAA,EAAM,QAAQ,CAAA;AAAA,MAC3C;AAEA,MAAA,GAAA,CAAI,CAAC,GAAA,EAAK;AACR,QAAA,GAAA,CAAI,CAAC,OAAA,CAAQ,GAAA,CAAI,gBAAA,GAAmB,CAAC,OAAA,CAAQ,GAAA,CAAI,iBAAA,EAAmB;AAClE,UAAA,MAAM,IAAI,KAAA;AAAA,YACR;AAAA,UACF,CAAA;AAAA,QACF;AAEA,QAAA,OAAA,CAAQ,IAAA;AAAA,UACN;AAAA,QACF,CAAA;AAEA,QAAA,IAAA,EAAM,YAAA,CAAa;AAAA,UACjB,GAAA,EAAK,OAAA,CAAQ,GAAA,CAAI,eAAA;AAAA,UACjB,KAAA,EAAO,OAAA,CAAQ,GAAA,CAAI;AAAA,QACrB,CAAC,CAAA;AAAA,MACH;AAGA,MAAA,OAAO,OAAA,CAAQ,GAAA,CAAI,GAAA,EAAK,IAAI,CAAA;AAAA,IAC9B;AAAA,EACF;AACF,CAAA;AAEO,IAAM,GAAA,EAAK,IAAI,KAAA;AAAA,EACpB,CAAC,CAAA;AAAA,EACD;AAAA,IACE,GAAA,CAAI,MAAA,EAAQ,IAAA,EAAM;AAChB,MAAA,GAAA,CAAI,CAAC,GAAA,EAAK;AACR,QAAA,GAAA,CAAI,CAAC,OAAA,CAAQ,GAAA,CAAI,gBAAA,GAAmB,CAAC,OAAA,CAAQ,GAAA,CAAI,iBAAA,EAAmB;AAClE,UAAA,MAAM,IAAI,KAAA;AAAA,YACR;AAAA,UACF,CAAA;AAAA,QACF;AAEA,QAAA,IAAA,EAAM,YAAA,CAAa;AAAA,UACjB,GAAA,EAAK,OAAA,CAAQ,GAAA,CAAI,eAAA;AAAA,UACjB,KAAA,EAAO,OAAA,CAAQ,GAAA,CAAI;AAAA,QACrB,CAAC,CAAA;AAAA,MACH;AAGA,MAAA,OAAO,OAAA,CAAQ,GAAA,CAAI,GAAA,EAAK,IAAI,CAAA;AAAA,IAC9B;AAAA,EACF;AACF,CAAA;AD9BA;AACE;AACA;AACA;AACA;AACF,iHAAC", "file": "/home/<USER>/work/storage/storage/packages/kv/dist/index.cjs", "sourcesContent": [null, "import { Redis } from '@upstash/redis';\nimport type { ScanCommandOptions, RedisConfigNodejs } from '@upstash/redis';\n\nlet _kv: Redis | null = null;\nprocess.env.UPSTASH_DISABLE_TELEMETRY = '1';\n\nexport class VercelKV extends Redis {\n  // This API is based on https://github.com/redis/node-redis#scan-iterator which is not supported in @upstash/redis\n  /**\n   * Same as `scan` but returns an AsyncIterator to allow iteration via `for await`.\n   */\n  async *scanIterator(options?: ScanCommandOptions): AsyncIterable<string> {\n    let cursor = '0';\n    let keys: string[];\n    do {\n      // eslint-disable-next-line no-await-in-loop -- [@vercel/style-guide@5 migration]\n      [cursor, keys] = await this.scan(cursor, options);\n      for (const key of keys) {\n        yield key;\n      }\n    } while (cursor !== '0');\n  }\n\n  /**\n   * Same as `hscan` but returns an AsyncIterator to allow iteration via `for await`.\n   */\n  async *hscanIterator(\n    key: string,\n    options?: ScanCommandOptions,\n  ): AsyncIterable<string | number> {\n    let cursor = '0';\n    let items: (number | string)[];\n    do {\n      // eslint-disable-next-line no-await-in-loop -- [@vercel/style-guide@5 migration]\n      [cursor, items] = await this.hscan(key, cursor, options);\n      for (const item of items) {\n        yield item;\n      }\n    } while (cursor !== '0');\n  }\n\n  /**\n   * Same as `sscan` but returns an AsyncIterator to allow iteration via `for await`.\n   */\n  async *sscanIterator(\n    key: string,\n    options?: ScanCommandOptions,\n  ): AsyncIterable<string | number> {\n    let cursor = '0';\n    let items: (number | string)[];\n    do {\n      // eslint-disable-next-line no-await-in-loop -- [@vercel/style-guide@5 migration]\n      [cursor, items] = await this.sscan(key, cursor, options);\n      for (const item of items) {\n        yield item;\n      }\n    } while (cursor !== '0');\n  }\n\n  /**\n   * Same as `zscan` but returns an AsyncIterator to allow iteration via `for await`.\n   */\n  async *zscanIterator(\n    key: string,\n    options?: ScanCommandOptions,\n  ): AsyncIterable<string | number> {\n    let cursor = '0';\n    let items: (number | string)[];\n    do {\n      // eslint-disable-next-line no-await-in-loop -- [@vercel/style-guide@5 migration]\n      [cursor, items] = await this.zscan(key, cursor, options);\n      for (const item of items) {\n        yield item;\n      }\n    } while (cursor !== '0');\n  }\n}\n\nexport function createClient(config: RedisConfigNodejs): VercelKV {\n  return new VercelKV({\n    // The Next.js team recommends no value or `default` for fetch requests's `cache` option\n    // upstash/redis defaults to `no-store`, so we enforce `default`\n    cache: 'default',\n    enableAutoPipelining: true,\n    ...config,\n  });\n}\n\n// eslint-disable-next-line import/no-default-export -- [@vercel/style-guide@5 migration]\nexport default new Proxy(\n  {},\n  {\n    get(target, prop, receiver) {\n      if (prop === 'then' || prop === 'parse') {\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-return -- [@vercel/style-guide@5 migration]\n        return Reflect.get(target, prop, receiver);\n      }\n\n      if (!_kv) {\n        if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {\n          throw new Error(\n            '@vercel/kv: Missing required environment variables KV_REST_API_URL and KV_REST_API_TOKEN',\n          );\n        }\n        // eslint-disable-next-line no-console -- [@vercel/style-guide@5 migration]\n        console.warn(\n          '\\x1b[33m\"The default export has been moved to a named export and it will be removed in version 1, change to import { kv }\\x1b[0m\"',\n        );\n\n        _kv = createClient({\n          url: process.env.KV_REST_API_URL,\n          token: process.env.KV_REST_API_TOKEN,\n        });\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-return -- [@vercel/style-guide@5 migration]\n      return Reflect.get(_kv, prop);\n    },\n  },\n) as VercelKV;\n\nexport const kv = new Proxy(\n  {},\n  {\n    get(target, prop) {\n      if (!_kv) {\n        if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {\n          throw new Error(\n            '@vercel/kv: Missing required environment variables KV_REST_API_URL and KV_REST_API_TOKEN',\n          );\n        }\n\n        _kv = createClient({\n          url: process.env.KV_REST_API_URL,\n          token: process.env.KV_REST_API_TOKEN,\n        });\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-return -- [@vercel/style-guide@5 migration]\n      return Reflect.get(_kv, prop);\n    },\n  },\n) as VercelKV;\n"]}