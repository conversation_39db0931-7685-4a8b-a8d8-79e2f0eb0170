import { Redis, ScanCommandOptions, RedisConfigNodejs } from '@upstash/redis';

declare class VercelKV extends Redis {
    /**
     * Same as `scan` but returns an AsyncIterator to allow iteration via `for await`.
     */
    scanIterator(options?: ScanCommandOptions): AsyncIterable<string>;
    /**
     * Same as `hscan` but returns an AsyncIterator to allow iteration via `for await`.
     */
    hscanIterator(key: string, options?: ScanCommandOptions): AsyncIterable<string | number>;
    /**
     * Same as `sscan` but returns an AsyncIterator to allow iteration via `for await`.
     */
    sscanIterator(key: string, options?: ScanCommandOptions): AsyncIterable<string | number>;
    /**
     * Same as `zscan` but returns an AsyncIterator to allow iteration via `for await`.
     */
    zscanIterator(key: string, options?: ScanCommandOptions): AsyncIterable<string | number>;
}
declare function createClient(config: RedisConfigNodejs): VercelKV;
declare const _default: VercelKV;

declare const kv: VercelKV;

export { VercelKV, createClient, _default as default, kv };
