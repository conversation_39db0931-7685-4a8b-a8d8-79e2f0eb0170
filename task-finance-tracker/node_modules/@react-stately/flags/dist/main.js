
function $parcel$export(e, n, v, s) {
  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});
}

$parcel$export(module.exports, "enableTableNestedRows", () => $b8649bdbb43830e8$export$d9d8a0f82de49530);
$parcel$export(module.exports, "tableNestedRows", () => $b8649bdbb43830e8$export$1b00cb14a96194e6);
$parcel$export(module.exports, "enableShadowDOM", () => $b8649bdbb43830e8$export$12b151d9882e9985);
$parcel$export(module.exports, "shadowDOM", () => $b8649bdbb43830e8$export$98658e8c59125e6a);
/*
 * Copyright 2023 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ let $b8649bdbb43830e8$var$_tableNestedRows = false;
let $b8649bdbb43830e8$var$_shadowDOM = false;
function $b8649bdbb43830e8$export$d9d8a0f82de49530() {
    $b8649bdbb43830e8$var$_tableNestedRows = true;
}
function $b8649bdbb43830e8$export$1b00cb14a96194e6() {
    return $b8649bdbb43830e8$var$_tableNestedRows;
}
function $b8649bdbb43830e8$export$12b151d9882e9985() {
    $b8649bdbb43830e8$var$_shadowDOM = true;
}
function $b8649bdbb43830e8$export$98658e8c59125e6a() {
    return $b8649bdbb43830e8$var$_shadowDOM;
}


//# sourceMappingURL=main.js.map
