'use client';

import { useState } from 'react';
import { Task } from '@/lib/kv';
import { 
  ChevronRightIcon, 
  ChevronDownIcon,
  PencilIcon,
  TrashIcon,
  CalendarIcon,
  CurrencyYenIcon
} from '@heroicons/react/24/outline';

interface TaskWithChildren extends Task {
  children: TaskWithChildren[];
}

interface TaskListProps {
  tasks: TaskWithChildren[];
  onEdit: (task: Task) => void;
  onDelete: (taskId: string) => void;
  onStatusChange: (taskId: string, status: string) => void;
}

interface TaskItemProps {
  task: TaskWithChildren;
  level: number;
  onEdit: (task: Task) => void;
  onDelete: (taskId: string) => void;
  onStatusChange: (taskId: string, status: string) => void;
}

const statusLabels = {
  not_started: '未着手',
  in_progress: '進行中',
  completed: '完了',
};

const statusColors = {
  not_started: 'bg-gray-100 text-gray-800',
  in_progress: 'bg-blue-100 text-blue-800',
  completed: 'bg-green-100 text-green-800',
};

const priorityLabels = {
  low: '低',
  medium: '中',
  high: '高',
};

const priorityColors = {
  low: 'bg-gray-100 text-gray-800',
  medium: 'bg-yellow-100 text-yellow-800',
  high: 'bg-red-100 text-red-800',
};

function TaskItem({ task, level, onEdit, onDelete, onStatusChange }: TaskItemProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const hasChildren = task.children && task.children.length > 0;

  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onStatusChange(task.id, e.target.value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ja-JP');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ja-JP').format(amount);
  };

  return (
    <div className="border-l-2 border-gray-200">
      <div
        className={`bg-white border border-gray-200 rounded-lg p-4 mb-2 shadow-sm hover:shadow-md transition-shadow ${
          level > 0 ? 'ml-6' : ''
        }`}
      >
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center mb-2">
              {hasChildren && (
                <button
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="mr-2 p-1 hover:bg-gray-100 rounded text-gray-600 hover:text-gray-800"
                >
                  {isExpanded ? (
                    <ChevronDownIcon className="h-4 w-4" />
                  ) : (
                    <ChevronRightIcon className="h-4 w-4" />
                  )}
                </button>
              )}
              <h3 className={`text-lg font-medium ${
                task.status === 'completed' ? 'line-through text-gray-500' : 'text-gray-900'
              }`}>
                {task.title}
              </h3>
            </div>

            {task.description && (
              <p className="text-gray-600 mb-3">{task.description}</p>
            )}

            <div className="flex flex-wrap items-center gap-2 mb-3">
              {/* ステータス */}
              <select
                value={task.status}
                onChange={handleStatusChange}
                className={`px-2 py-1 text-xs font-medium rounded-full border-0 cursor-pointer ${statusColors[task.status]}`}
                style={{
                  backgroundColor: task.status === 'not_started' ? '#f3f4f6' :
                                   task.status === 'in_progress' ? '#dbeafe' : '#dcfce7',
                  color: task.status === 'not_started' ? '#374151' :
                         task.status === 'in_progress' ? '#1e40af' : '#166534'
                }}
              >
                <option value="not_started">未着手</option>
                <option value="in_progress">進行中</option>
                <option value="completed">完了</option>
              </select>

              {/* 優先度 */}
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${priorityColors[task.priority]}`}>
                優先度: {priorityLabels[task.priority]}
              </span>

              {/* 目標日 */}
              {task.targetDate && (
                <span className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-gray-100 rounded-full">
                  <CalendarIcon className="h-3 w-3 mr-1 text-gray-500" />
                  {formatDate(task.targetDate)}
                </span>
              )}

              {/* 財務影響 */}
              {task.financialImpact && (
                <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${
                  task.financialImpact.type === 'income'
                    ? 'text-green-700 bg-green-100'
                    : 'text-red-700 bg-red-100'
                }`}>
                  <CurrencyYenIcon className={`h-3 w-3 mr-1 ${
                    task.financialImpact.type === 'income' ? 'text-green-600' : 'text-red-600'
                  }`} />
                  {task.financialImpact.type === 'income' ? '+' : '-'}
                  {formatCurrency(task.financialImpact.amount)}
                </span>
              )}
            </div>

            {/* 子タスクの進捗 */}
            {hasChildren && (
              <div className="mb-2">
                <div className="flex items-center text-sm text-gray-600">
                  <span>
                    子タスク: {task.children.filter(c => c.status === 'completed').length} / {task.children.length} 完了
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ 
                      width: `${task.children.length > 0 
                        ? (task.children.filter(c => c.status === 'completed').length / task.children.length) * 100 
                        : 0}%` 
                    }}
                  ></div>
                </div>
              </div>
            )}

            <div className="text-xs text-gray-500">
              作成: {formatDate(task.createdAt)} | 更新: {formatDate(task.updatedAt)}
            </div>
          </div>

          {/* アクション */}
          <div className="flex items-center space-x-2 ml-4">
            <button
              onClick={() => onEdit(task)}
              className="p-2 text-gray-500 hover:text-indigo-600 hover:bg-indigo-50 rounded transition-colors"
              title="編集"
            >
              <PencilIcon className="h-4 w-4" />
            </button>
            <button
              onClick={() => onDelete(task.id)}
              className="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
              title="削除"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* 子タスク */}
      {hasChildren && isExpanded && (
        <div className="ml-4">
          {task.children.map((childTask) => (
            <TaskItem
              key={childTask.id}
              task={childTask}
              level={level + 1}
              onEdit={onEdit}
              onDelete={onDelete}
              onStatusChange={onStatusChange}
            />
          ))}
        </div>
      )}
    </div>
  );
}

export default function TaskList({ tasks, onEdit, onDelete, onStatusChange }: TaskListProps) {
  if (tasks.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 mb-4">タスクがありません</div>
        <p className="text-sm text-gray-400">
          「新しいタスク」ボタンをクリックしてタスクを作成してください
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {tasks.map((task) => (
        <TaskItem
          key={task.id}
          task={task}
          level={0}
          onEdit={onEdit}
          onDelete={onDelete}
          onStatusChange={onStatusChange}
        />
      ))}
    </div>
  );
}
