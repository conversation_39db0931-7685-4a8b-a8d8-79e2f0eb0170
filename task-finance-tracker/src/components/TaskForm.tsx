'use client';

import { useState, useEffect } from 'react';
import { Dialog } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { Task } from '@/lib/kv';

interface TaskFormProps {
  task?: Task | null;
  onSave: () => void;
  onCancel: () => void;
}

interface FormData {
  title: string;
  description: string;
  targetDate: string;
  priority: 'low' | 'medium' | 'high';
  parentId: string;
  financialImpact: {
    enabled: boolean;
    type: 'income' | 'expense';
    amount: string;
    description: string;
  };
}

export default function TaskForm({ task, onSave, onCancel }: TaskFormProps) {
  const [formData, setFormData] = useState<FormData>({
    title: '',
    description: '',
    targetDate: '',
    priority: 'medium',
    parentId: '',
    financialImpact: {
      enabled: false,
      type: 'expense',
      amount: '',
      description: '',
    },
  });
  const [parentTasks, setParentTasks] = useState<Task[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 編集時の初期値設定
  useEffect(() => {
    if (task) {
      setFormData({
        title: task.title,
        description: task.description || '',
        targetDate: task.targetDate || '',
        priority: task.priority,
        parentId: task.parentId || '',
        financialImpact: {
          enabled: !!task.financialImpact,
          type: task.financialImpact?.type || 'expense',
          amount: task.financialImpact?.amount?.toString() || '',
          description: task.financialImpact?.description || '',
        },
      });
    }
  }, [task]);

  // 親タスク候補を取得
  useEffect(() => {
    const fetchParentTasks = async () => {
      try {
        const response = await fetch('/api/tasks?parentId=');
        if (response.ok) {
          const data = await response.json();
          // 編集中のタスクとその子タスクは除外
          const filteredTasks = data.tasks.filter((t: Task) => 
            t.id !== task?.id && t.parentId !== task?.id
          );
          setParentTasks(filteredTasks);
        }
      } catch (error) {
        console.error('Error fetching parent tasks:', error);
      }
    };

    fetchParentTasks();
  }, [task]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      // バリデーション
      if (!formData.title.trim()) {
        throw new Error('タイトルは必須です');
      }

      if (formData.financialImpact.enabled) {
        if (!formData.financialImpact.amount || isNaN(Number(formData.financialImpact.amount))) {
          throw new Error('財務影響の金額は数値で入力してください');
        }
        if (Number(formData.financialImpact.amount) <= 0) {
          throw new Error('財務影響の金額は正の数値で入力してください');
        }
      }

      // リクエストデータを構築
      const requestData = {
        title: formData.title.trim(),
        description: formData.description.trim() || undefined,
        targetDate: formData.targetDate || undefined,
        priority: formData.priority,
        parentId: formData.parentId || undefined,
        financialImpact: formData.financialImpact.enabled ? {
          type: formData.financialImpact.type,
          amount: Number(formData.financialImpact.amount),
          description: formData.financialImpact.description.trim() || undefined,
        } : undefined,
      };

      // API呼び出し
      const url = task ? `/api/tasks/${task.id}` : '/api/tasks';
      const method = task ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'タスクの保存に失敗しました');
      }

      onSave();
    } catch (error) {
      console.error('Error saving task:', error);
      setError(error instanceof Error ? error.message : 'タスクの保存に失敗しました');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleFinancialImpactChange = (field: keyof FormData['financialImpact'], value: any) => {
    setFormData(prev => ({
      ...prev,
      financialImpact: {
        ...prev.financialImpact,
        [field]: value,
      },
    }));
  };

  return (
    <Dialog open={true} onClose={onCancel} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="mx-auto max-w-2xl w-full bg-white rounded-lg shadow-xl border border-gray-200">
          <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-white rounded-t-lg">
            <Dialog.Title className="text-lg font-medium text-gray-900">
              {task ? 'タスクを編集' : '新しいタスクを作成'}
            </Dialog.Title>
            <button
              onClick={onCancel}
              className="text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded transition-colors"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="p-6 space-y-6 bg-white">
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="text-sm text-red-700">{error}</div>
              </div>
            )}

            {/* タイトル */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                タイトル *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="タスクのタイトルを入力"
                required
              />
            </div>

            {/* 説明 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                説明
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={3}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="タスクの詳細説明（任意）"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 目標日 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  目標日
                </label>
                <input
                  type="date"
                  value={formData.targetDate}
                  onChange={(e) => handleInputChange('targetDate', e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>

              {/* 優先度 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  優先度
                </label>
                <select
                  value={formData.priority}
                  onChange={(e) => handleInputChange('priority', e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="low">低</option>
                  <option value="medium">中</option>
                  <option value="high">高</option>
                </select>
              </div>
            </div>

            {/* 親タスク */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                親タスク
              </label>
              <select
                value={formData.parentId}
                onChange={(e) => handleInputChange('parentId', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="">なし（ルートタスク）</option>
                {parentTasks.map((parentTask) => (
                  <option key={parentTask.id} value={parentTask.id}>
                    {parentTask.title}
                  </option>
                ))}
              </select>
            </div>

            {/* 財務影響 */}
            <div>
              <div className="flex items-center mb-3">
                <input
                  type="checkbox"
                  id="financialImpact"
                  checked={formData.financialImpact.enabled}
                  onChange={(e) => handleFinancialImpactChange('enabled', e.target.checked)}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="financialImpact" className="ml-2 block text-sm font-medium text-gray-700">
                  財務影響あり
                </label>
              </div>

              {formData.financialImpact.enabled && (
                <div className="space-y-4 pl-6 border-l-2 border-gray-200">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        種類
                      </label>
                      <select
                        value={formData.financialImpact.type}
                        onChange={(e) => handleFinancialImpactChange('type', e.target.value)}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                      >
                        <option value="income">収入</option>
                        <option value="expense">支出</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        金額（円）
                      </label>
                      <input
                        type="number"
                        value={formData.financialImpact.amount}
                        onChange={(e) => handleFinancialImpactChange('amount', e.target.value)}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="0"
                        min="0"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      財務影響の説明
                    </label>
                    <input
                      type="text"
                      value={formData.financialImpact.description}
                      onChange={(e) => handleFinancialImpactChange('description', e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                      placeholder="財務影響の詳細"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* ボタン */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 bg-white rounded-b-lg">
              <button
                type="button"
                onClick={onCancel}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
              >
                キャンセル
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                style={{ color: '#ffffff' }}
              >
                <span style={{ color: '#ffffff' }}>
                  {isLoading ? '保存中...' : (task ? '更新' : '作成')}
                </span>
              </button>
            </div>
          </form>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
}
