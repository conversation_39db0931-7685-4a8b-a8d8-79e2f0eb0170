import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

// 認証設定
const AUTH_COOKIE_NAME = 'auth-session';
const SESSION_DURATION = 24 * 60 * 60 * 1000; // 24時間

// 環境変数から認証情報を取得
const getAuthCredentials = () => {
  const username = process.env.AUTH_USERNAME;
  const password = process.env.AUTH_PASSWORD;
  
  if (!username || !password) {
    throw new Error('AUTH_USERNAME and AUTH_PASSWORD must be set in environment variables');
  }
  
  return { username, password };
};

// ログイン検証
export const validateLogin = (username: string, password: string): boolean => {
  try {
    const credentials = getAuthCredentials();
    return username === credentials.username && password === credentials.password;
  } catch (error) {
    console.error('Auth validation error:', error);
    return false;
  }
};

// セッション作成
export const createSession = async (): Promise<void> => {
  const cookieStore = await cookies();
  const sessionData = {
    authenticated: true,
    timestamp: Date.now(),
  };
  
  cookieStore.set(AUTH_COOKIE_NAME, JSON.stringify(sessionData), {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: SESSION_DURATION / 1000, // seconds
  });
};

// セッション検証
export const validateSession = async (): Promise<boolean> => {
  try {
    const cookieStore = await cookies();
    const sessionCookie = cookieStore.get(AUTH_COOKIE_NAME);
    
    if (!sessionCookie) {
      return false;
    }
    
    const sessionData = JSON.parse(sessionCookie.value);
    const now = Date.now();
    
    // セッションの有効期限チェック
    if (now - sessionData.timestamp > SESSION_DURATION) {
      return false;
    }
    
    return sessionData.authenticated === true;
  } catch (error) {
    console.error('Session validation error:', error);
    return false;
  }
};

// セッション削除
export const destroySession = async (): Promise<void> => {
  const cookieStore = await cookies();
  cookieStore.delete(AUTH_COOKIE_NAME);
};

// ミドルウェア用の認証チェック
export const checkAuthMiddleware = async (request: NextRequest): Promise<NextResponse | null> => {
  const { pathname } = request.nextUrl;
  
  // ログインページとAPIルートは除外
  if (pathname === '/login' || pathname.startsWith('/api/auth')) {
    return null;
  }
  
  try {
    const sessionCookie = request.cookies.get(AUTH_COOKIE_NAME);
    
    if (!sessionCookie) {
      return NextResponse.redirect(new URL('/login', request.url));
    }
    
    const sessionData = JSON.parse(sessionCookie.value);
    const now = Date.now();
    
    // セッションの有効期限チェック
    if (now - sessionData.timestamp > SESSION_DURATION || !sessionData.authenticated) {
      const response = NextResponse.redirect(new URL('/login', request.url));
      response.cookies.delete(AUTH_COOKIE_NAME);
      return response;
    }
    
    return null; // 認証OK、処理続行
  } catch (error) {
    console.error('Middleware auth error:', error);
    const response = NextResponse.redirect(new URL('/login', request.url));
    response.cookies.delete(AUTH_COOKIE_NAME);
    return response;
  }
};
