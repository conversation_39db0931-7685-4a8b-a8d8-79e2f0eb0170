import { kv } from '@vercel/kv';
import { MockKVClient } from './mock-kv';

// 開発環境かどうかを判定
const isDevelopment = process.env.NODE_ENV === 'development';
const hasKVConfig = process.env.KV_URL || process.env.KV_REST_API_URL;

// KVクライアントのラッパー関数
export class KVClient {
  // タスク関連のキー
  static TASKS_KEY = 'tasks';
  static TASK_COUNTER_KEY = 'task_counter';
  
  // 収支関連のキー
  static FINANCIAL_PLANS_KEY = 'financial_plans';
  static FINANCIAL_COUNTER_KEY = 'financial_counter';

  // タスクデータの取得
  static async getTasks(): Promise<Task[]> {
    if (isDevelopment && !hasKVConfig) {
      return MockKVClient.getTasks();
    }
    try {
      const tasks = await kv.get<Task[]>(this.TASKS_KEY);
      return tasks || [];
    } catch (error) {
      console.error('Error getting tasks:', error);
      return [];
    }
  }

  // タスクデータの保存
  static async saveTasks(tasks: Task[]): Promise<void> {
    if (isDevelopment && !hasKVConfig) {
      return MockKVClient.saveTasks(tasks);
    }
    try {
      await kv.set(this.TASKS_KEY, tasks);
    } catch (error) {
      console.error('Error saving tasks:', error);
      throw error;
    }
  }

  // 新しいタスクIDの生成
  static async getNextTaskId(): Promise<string> {
    if (isDevelopment && !hasKVConfig) {
      return MockKVClient.getNextTaskId();
    }
    try {
      const counter = await kv.incr(this.TASK_COUNTER_KEY);
      return `task_${counter}`;
    } catch (error) {
      console.error('Error generating task ID:', error);
      throw error;
    }
  }

  // 収支計画データの取得
  static async getFinancialPlans(): Promise<FinancialPlan[]> {
    if (isDevelopment && !hasKVConfig) {
      return MockKVClient.getFinancialPlans();
    }
    try {
      const plans = await kv.get<FinancialPlan[]>(this.FINANCIAL_PLANS_KEY);
      return plans || [];
    } catch (error) {
      console.error('Error getting financial plans:', error);
      return [];
    }
  }

  // 収支計画データの保存
  static async saveFinancialPlans(plans: FinancialPlan[]): Promise<void> {
    if (isDevelopment && !hasKVConfig) {
      return MockKVClient.saveFinancialPlans(plans);
    }
    try {
      await kv.set(this.FINANCIAL_PLANS_KEY, plans);
    } catch (error) {
      console.error('Error saving financial plans:', error);
      throw error;
    }
  }

  // 新しい収支計画IDの生成
  static async getNextFinancialId(): Promise<string> {
    if (isDevelopment && !hasKVConfig) {
      return MockKVClient.getNextFinancialId();
    }
    try {
      const counter = await kv.incr(this.FINANCIAL_COUNTER_KEY);
      return `financial_${counter}`;
    } catch (error) {
      console.error('Error generating financial ID:', error);
      throw error;
    }
  }

  // 開発用：全データのクリア
  static async clearAllData(): Promise<void> {
    if (process.env.NODE_ENV !== 'development') {
      throw new Error('Data clearing is only allowed in development mode');
    }

    if (isDevelopment && !hasKVConfig) {
      return MockKVClient.clearAllData();
    }

    try {
      await Promise.all([
        kv.del(this.TASKS_KEY),
        kv.del(this.TASK_COUNTER_KEY),
        kv.del(this.FINANCIAL_PLANS_KEY),
        kv.del(this.FINANCIAL_COUNTER_KEY),
      ]);
    } catch (error) {
      console.error('Error clearing data:', error);
      throw error;
    }
  }

  // ヘルスチェック
  static async healthCheck(): Promise<boolean> {
    if (isDevelopment && !hasKVConfig) {
      return MockKVClient.healthCheck();
    }
    try {
      await kv.set('health_check', Date.now(), { ex: 60 }); // 60秒で期限切れ
      const result = await kv.get('health_check');
      return result !== null;
    } catch (error) {
      console.error('KV health check failed:', error);
      return false;
    }
  }
}

// 型定義
export interface Task {
  id: string;
  title: string;
  description?: string;
  status: 'not_started' | 'in_progress' | 'completed';
  targetDate?: string;
  priority: 'low' | 'medium' | 'high';
  parentId?: string;
  financialImpact?: {
    type: 'income' | 'expense';
    amount: number;
    description: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface FinancialPlan {
  id: string;
  type: 'income_target' | 'expense_plan' | 'asset';
  title: string;
  amount: number;
  period: 'monthly' | 'yearly' | 'one_time';
  targetDate?: string;
  actualAmount?: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}
