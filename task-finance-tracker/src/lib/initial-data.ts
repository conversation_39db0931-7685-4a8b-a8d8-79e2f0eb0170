import { Task, FinancialPlan } from './kv';

// 初期タスクデータ（TodoList.mdから移行）
export const initialTasks: Omit<Task, 'id'>[] = [
  // 7月のタスク
  {
    title: '7月：顧客対応と住居確定',
    description: '住居の最終判断と顧客への退職説明',
    status: 'not_started',
    targetDate: '2024-07-31',
    priority: 'high',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    title: '内見結果を踏まえた住居の最終判断',
    description: '来週の内見後に住居を確定する',
    status: 'not_started',
    targetDate: '2024-07-07',
    priority: 'high',
    parentId: 'task_1',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    title: '購入決定なら住宅ローン等の手続き開始',
    description: '住居購入が決定した場合の手続き',
    status: 'not_started',
    targetDate: '2024-07-14',
    priority: 'high',
    parentId: 'task_1',
    financialImpact: {
      type: 'expense',
      amount: 10000000,
      description: '住居購入費用'
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    title: '顧客への退職説明を本格開始',
    description: '週2-3社ペースで顧客に説明',
    status: 'not_started',
    targetDate: '2024-07-31',
    priority: 'high',
    parentId: 'task_1',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  
  // 8月のタスク
  {
    title: '8月：奥様移住と収入確定',
    description: '奥様の移住サポートと退職後収入の確定',
    status: 'not_started',
    targetDate: '2024-08-31',
    priority: 'high',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    title: '奥様の移住実行とサポート',
    description: '8月前半の奥様移住サポート',
    status: 'not_started',
    targetDate: '2024-08-15',
    priority: 'high',
    parentId: 'task_5',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    title: '新居での生活環境セットアップ',
    description: '生活に必要な環境の整備',
    status: 'not_started',
    targetDate: '2024-08-15',
    priority: 'medium',
    parentId: 'task_5',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    title: '退職後収入の確定',
    description: '目標：年800-1000万円',
    status: 'not_started',
    targetDate: '2024-08-31',
    priority: 'high',
    parentId: 'task_5',
    financialImpact: {
      type: 'income',
      amount: 9000000,
      description: '退職後年収目標（平均値）'
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  
  // 9月のタスク
  {
    title: '9月：ご自身の移住と新体制移行',
    description: '月2回出社体制での移住と引き継ぎ完了',
    status: 'not_started',
    targetDate: '2024-09-30',
    priority: 'high',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    title: '月2回出社体制スタートに合わせて移住',
    description: '新しい働き方での移住実行',
    status: 'not_started',
    targetDate: '2024-09-15',
    priority: 'high',
    parentId: 'task_9',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    title: '仕事環境の最終調整',
    description: 'リモートワーク環境の完成',
    status: 'not_started',
    targetDate: '2024-09-20',
    priority: 'medium',
    parentId: 'task_9',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    title: '引き継ぎ完了に向けた最終段階',
    description: '業務引き継ぎの完了',
    status: 'not_started',
    targetDate: '2024-09-30',
    priority: 'high',
    parentId: 'task_9',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

// 初期収支計画データ
export const initialFinancialPlans: Omit<FinancialPlan, 'id'>[] = [
  {
    type: 'income_target',
    title: '退職後年収目標（最低）',
    amount: 8000000,
    period: 'yearly',
    targetDate: '2024-12-31',
    notes: '年800万円の収入確保',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    type: 'income_target',
    title: '退職後年収目標（理想）',
    amount: 10000000,
    period: 'yearly',
    targetDate: '2024-12-31',
    notes: '年1000万円の収入確保',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    type: 'expense_plan',
    title: '月間生活費',
    amount: 300000,
    period: 'monthly',
    notes: '基本的な生活費',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    type: 'asset',
    title: '現在の資産',
    amount: 25000000,
    period: 'one_time',
    notes: '現在保有している資産',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    type: 'expense_plan',
    title: '住居購入費用',
    amount: 10000000,
    period: 'one_time',
    targetDate: '2024-08-31',
    notes: '住居購入予算',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    type: 'expense_plan',
    title: '固定資産税・維持費',
    amount: 250000,
    period: 'yearly',
    notes: '住居関連の年間維持費',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];
