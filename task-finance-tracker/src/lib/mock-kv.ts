import { Task, FinancialPlan } from './kv';
import { initialTasks, initialFinancialPlans } from './initial-data';

// 開発環境用のインメモリストレージ
class MockKVStorage {
  private storage: Map<string, any> = new Map();
  private counters: Map<string, number> = new Map();

  async get<T>(key: string): Promise<T | null> {
    return this.storage.get(key) || null;
  }

  async set(key: string, value: any): Promise<void> {
    this.storage.set(key, value);
  }

  async del(key: string): Promise<void> {
    this.storage.delete(key);
  }

  async incr(key: string): Promise<number> {
    const current = this.counters.get(key) || 0;
    const next = current + 1;
    this.counters.set(key, next);
    return next;
  }

  // 初期データを設定
  async initializeData(): Promise<void> {
    // タスクデータの初期化
    const tasksWithIds: Task[] = [];
    for (let i = 0; i < initialTasks.length; i++) {
      const taskData = initialTasks[i];
      const id = `task_${i + 1}`;
      tasksWithIds.push({
        ...taskData,
        id,
        // parentIdの調整
        parentId: taskData.parentId ? `task_${taskData.parentId.split('_')[1]}` : undefined,
      });
    }

    // 収支計画データの初期化
    const plansWithIds: FinancialPlan[] = [];
    for (let i = 0; i < initialFinancialPlans.length; i++) {
      const planData = initialFinancialPlans[i];
      const id = `financial_${i + 1}`;
      plansWithIds.push({
        ...planData,
        id,
      });
    }

    await this.set('tasks', tasksWithIds);
    await this.set('financial_plans', plansWithIds);
    this.counters.set('task_counter', tasksWithIds.length);
    this.counters.set('financial_counter', plansWithIds.length);
  }
}

// グローバルなモックストレージインスタンス
const mockStorage = new MockKVStorage();

// 開発環境用のKVクライアント
export class MockKVClient {
  static TASKS_KEY = 'tasks';
  static TASK_COUNTER_KEY = 'task_counter';
  static FINANCIAL_PLANS_KEY = 'financial_plans';
  static FINANCIAL_COUNTER_KEY = 'financial_counter';

  // 初期化フラグ
  private static initialized = false;

  // 初期化
  static async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await mockStorage.initializeData();
      this.initialized = true;
    }
  }

  static async getTasks(): Promise<Task[]> {
    await this.ensureInitialized();
    try {
      const tasks = await mockStorage.get<Task[]>(this.TASKS_KEY);
      return tasks || [];
    } catch (error) {
      console.error('Error getting tasks:', error);
      return [];
    }
  }

  static async saveTasks(tasks: Task[]): Promise<void> {
    await this.ensureInitialized();
    try {
      await mockStorage.set(this.TASKS_KEY, tasks);
    } catch (error) {
      console.error('Error saving tasks:', error);
      throw error;
    }
  }

  static async getNextTaskId(): Promise<string> {
    await this.ensureInitialized();
    try {
      const counter = await mockStorage.incr(this.TASK_COUNTER_KEY);
      return `task_${counter}`;
    } catch (error) {
      console.error('Error generating task ID:', error);
      throw error;
    }
  }

  static async getFinancialPlans(): Promise<FinancialPlan[]> {
    await this.ensureInitialized();
    try {
      const plans = await mockStorage.get<FinancialPlan[]>(this.FINANCIAL_PLANS_KEY);
      return plans || [];
    } catch (error) {
      console.error('Error getting financial plans:', error);
      return [];
    }
  }

  static async saveFinancialPlans(plans: FinancialPlan[]): Promise<void> {
    await this.ensureInitialized();
    try {
      await mockStorage.set(this.FINANCIAL_PLANS_KEY, plans);
    } catch (error) {
      console.error('Error saving financial plans:', error);
      throw error;
    }
  }

  static async getNextFinancialId(): Promise<string> {
    await this.ensureInitialized();
    try {
      const counter = await mockStorage.incr(this.FINANCIAL_COUNTER_KEY);
      return `financial_${counter}`;
    } catch (error) {
      console.error('Error generating financial ID:', error);
      throw error;
    }
  }

  static async clearAllData(): Promise<void> {
    try {
      await Promise.all([
        mockStorage.del(this.TASKS_KEY),
        mockStorage.del(this.TASK_COUNTER_KEY),
        mockStorage.del(this.FINANCIAL_PLANS_KEY),
        mockStorage.del(this.FINANCIAL_COUNTER_KEY),
      ]);
      this.initialized = false;
    } catch (error) {
      console.error('Error clearing data:', error);
      throw error;
    }
  }

  static async healthCheck(): Promise<boolean> {
    try {
      await mockStorage.set('health_check', Date.now());
      const result = await mockStorage.get('health_check');
      return result !== null;
    } catch (error) {
      console.error('Mock KV health check failed:', error);
      return false;
    }
  }
}
