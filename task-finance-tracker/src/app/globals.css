@tailwind base;
@tailwind components;
@tailwind utilities;

/* 基本的なリセットとベーススタイル */
* {
  box-sizing: border-box;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  background-color: #ffffff; /* white */
  color: #000000; /* black */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
}

/* フォーム要素のスタイル */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="date"],
textarea,
select {
  background-color: #ffffff !important;
  border: 1px solid #000000 !important; /* black */
  color: #000000 !important; /* black */
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
input[type="date"]:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: #000000 !important; /* black */
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1) !important;
}

input[type="text"]:disabled,
input[type="email"]:disabled,
input[type="password"]:disabled,
input[type="number"]:disabled,
input[type="date"]:disabled,
textarea:disabled,
select:disabled {
  background-color: #f3f4f6 !important; /* gray-100 */
  color: #6b7280 !important; /* gray-500 */
  cursor: not-allowed;
}

/* ボタンのスタイル */
button {
  background-color: #ffffff !important;
  border: 1px solid #000000 !important;
  color: #000000 !important;
  cursor: pointer;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  border-radius: 0.375rem;
}

button:hover {
  background-color: #f3f4f6 !important;
}

/* プライマリボタン */
.btn-primary {
  background-color: #ffffff !important;
  color: #000000 !important;
  border: 1px solid #000000 !important;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.15s ease-in-out;
}

.btn-primary:hover {
  background-color: #f3f4f6 !important;
  color: #000000 !important;
}

.btn-primary:disabled {
  background-color: #f3f4f6 !important;
  color: #6b7280 !important;
  cursor: not-allowed;
}

/* インディゴボタンの強制スタイル */
.bg-indigo-600,
.bg-indigo-700 {
  background-color: #ffffff !important;
  color: #000000 !important;
  border: 1px solid #000000 !important;
}

.bg-indigo-600 *,
.bg-indigo-700 * {
  color: #000000 !important;
}

/* セカンダリボタン */
.btn-secondary {
  background-color: #ffffff !important;
  color: #000000 !important;
  border: 1px solid #000000 !important;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.15s ease-in-out;
}

.btn-secondary:hover {
  background-color: #f3f4f6 !important;
}

/* リンクのスタイル */
a {
  color: #000000 !important;
  text-decoration: none;
  transition: color 0.15s ease-in-out;
}

a:hover {
  color: #000000 !important;
  text-decoration: underline;
}

/* カードのスタイル */
.card {
  background-color: #ffffff !important;
  border: 1px solid #000000 !important;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* ダイアログのスタイル */
[role="dialog"] {
  background-color: #ffffff !important;
  color: #000000 !important;
}

/* アイコンのデフォルトスタイル */
svg {
  color: #000000 !important;
}

/* インタラクティブ要素のトランジション */
button, a, select, input, textarea {
  transition: all 0.15s ease-in-out;
}

/* フォーカス可能な要素の基本スタイル */
button:focus-visible,
a:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible {
  outline: 2px solid #000000 !important;
  outline-offset: 2px;
}

/* 無効化された要素のスタイル */
button:disabled,
input:disabled,
textarea:disabled,
select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* テキストカラーの修正 */
.text-gray-500,
.text-gray-600,
.text-gray-700,
.text-gray-800,
.text-gray-900 {
  color: #000000 !important;
}

/* アイコンカラーの修正 */
.h-3, .h-4, .h-5, .h-6 {
  color: #000000 !important;
}

/* ボタン内のテキストとアイコンの修正 */
button.bg-indigo-600 *,
button.hover\:bg-indigo-700 *,
button.bg-indigo-700 *,
button * {
  color: #000000 !important;
}

/* ログインページのスタイル修正 */
.max-w-md {
  max-width: 28rem;
}

.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

.bg-gray-50 {
  background-color: #ffffff !important;
}

.-space-y-px > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(-1px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(-1px * var(--tw-space-y-reverse));
}

/* 背景色の統一 */
.bg-white {
  background-color: #ffffff !important;
}

/* プレースホルダーの色 */
::placeholder {
  color: #6b7280 !important; /* gray-500 */
  opacity: 1;
}

/* 特定のTailwindクラスの上書き */
.placeholder-gray-500 {
  color: #6b7280 !important;
}

.text-white {
  color: #000000 !important;
}

/* Tailwindクラスの上書き */
.bg-indigo-600 {
  background-color: #ffffff !important;
}

.hover\:bg-indigo-700:hover {
  background-color: #f3f4f6 !important;
}

.text-indigo-600,
.text-indigo-700,
.text-red-600,
.text-red-700,
.text-blue-600,
.text-blue-700,
.text-green-600,
.text-green-700 {
  color: #000000 !important;
}

/* ステータスバッジの修正 */
.bg-gray-100,
.bg-blue-100,
.bg-green-100,
.bg-red-100,
.bg-yellow-100 {
  background-color: #ffffff !important;
  border: 1px solid #000000 !important;
}

.text-gray-800,
.text-blue-800,
.text-green-800,
.text-red-800,
.text-yellow-800 {
  color: #000000 !important;
}
