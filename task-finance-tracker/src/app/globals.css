@import "tailwindcss";

/* 基本的なリセットとベーススタイル */
* {
  box-sizing: border-box;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  background-color: #f9fafb; /* gray-50 */
  color: #111827; /* gray-900 */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
}

/* フォーム要素のスタイル */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="date"],
textarea,
select {
  background-color: #ffffff;
  border: 1px solid #d1d5db; /* gray-300 */
  color: #111827; /* gray-900 */
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
input[type="date"]:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: #6366f1; /* indigo-500 */
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

input[type="text"]:disabled,
input[type="email"]:disabled,
input[type="password"]:disabled,
input[type="number"]:disabled,
input[type="date"]:disabled,
textarea:disabled,
select:disabled {
  background-color: #f3f4f6; /* gray-100 */
  color: #6b7280; /* gray-500 */
  cursor: not-allowed;
}

/* ボタンのスタイル */
button {
  background-color: transparent;
  border: none;
  cursor: pointer;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

/* プライマリボタン */
.btn-primary {
  background-color: #6366f1; /* indigo-600 */
  color: #ffffff;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.15s ease-in-out;
}

.btn-primary:hover {
  background-color: #4f46e5; /* indigo-700 */
}

.btn-primary:disabled {
  background-color: #9ca3af; /* gray-400 */
  cursor: not-allowed;
}

/* セカンダリボタン */
.btn-secondary {
  background-color: #ffffff;
  color: #374151; /* gray-700 */
  border: 1px solid #d1d5db; /* gray-300 */
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.15s ease-in-out;
}

.btn-secondary:hover {
  background-color: #f9fafb; /* gray-50 */
}

/* リンクのスタイル */
a {
  color: #6366f1; /* indigo-600 */
  text-decoration: none;
  transition: color 0.15s ease-in-out;
}

a:hover {
  color: #4f46e5; /* indigo-700 */
  text-decoration: underline;
}

/* カードのスタイル */
.card {
  background-color: #ffffff;
  border: 1px solid #e5e7eb; /* gray-200 */
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* ダークモード対応 */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #111827; /* gray-900 */
    color: #f9fafb; /* gray-50 */
  }

  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="date"],
  textarea,
  select {
    background-color: #374151; /* gray-700 */
    border-color: #4b5563; /* gray-600 */
    color: #f9fafb; /* gray-50 */
  }

  .card {
    background-color: #1f2937; /* gray-800 */
    border-color: #374151; /* gray-700 */
  }

  .btn-secondary {
    background-color: #374151; /* gray-700 */
    color: #f9fafb; /* gray-50 */
    border-color: #4b5563; /* gray-600 */
  }

  .btn-secondary:hover {
    background-color: #4b5563; /* gray-600 */
  }
}
