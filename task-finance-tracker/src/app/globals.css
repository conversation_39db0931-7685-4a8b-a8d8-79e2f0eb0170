@tailwind base;
@tailwind components;
@tailwind utilities;

/* カスタムスタイル */
body {
  background: linear-gradient(135deg, #e0e7ff 0%, #f3e8ff 50%, #fce7f3 100%);
  min-height: 100vh;
}

/* カードスタイル */
.bg-white {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* ボタンスタイル */
.bg-indigo-600 {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.4);
  transition: all 0.3s ease;
}

.bg-indigo-600:hover,
.hover\:bg-indigo-700:hover {
  background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.6);
}

/* フォーム要素 */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="date"],
textarea,
select {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(209, 213, 219, 0.5);
  border-radius: 12px;
  transition: all 0.3s ease;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
input[type="date"]:focus,
textarea:focus,
select:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  transform: translateY(-1px);
}

/* シャドウ強化 */
.shadow {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.shadow-lg {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.shadow-xl {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}


