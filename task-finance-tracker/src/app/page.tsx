'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';

interface TaskStats {
  overall: {
    total: number;
    completed: number;
    inProgress: number;
    notStarted: number;
    completionRate: number;
  };
  byParent: Record<string, {
    total: number;
    completed: number;
    inProgress: number;
    notStarted: number;
    title: string;
  }>;
}

export default function Home() {
  const [stats, setStats] = useState<TaskStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/tasks/hierarchy', {
          method: 'POST',
        });
        if (response.ok) {
          const data = await response.json();
          setStats(data);
        }
      } catch (error) {
        console.error('Error fetching stats:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
  }, []);
  return (
    <div className="min-h-screen">
      <Header />
      <main className="container py-8">
        <div className="mb-8 fade-in">
          <h1>ダッシュボード</h1>
          <p>退職・移住計画の進捗状況と収支管理の概要</p>
        </div>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 slide-in">
          {/* タスク進捗サマリー */}
          <div className="card">
            <h2>タスク進捗サマリー</h2>
              {isLoading ? (
                <div className="text-center py-4 text-gray-700">読み込み中...</div>
              ) : stats ? (
                <div className="space-y-4">
                  {/* 全体進捗 */}
                  <div className="mb-6">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-900">全体進捗</span>
                      <span className="text-sm font-medium text-gray-900">
                        {stats.overall.completed}/{stats.overall.total} 完了 ({stats.overall.completionRate}%)
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                        style={{ width: `${stats.overall.completionRate}%` }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-xs text-gray-700 mt-1">
                      <span>未着手: {stats.overall.notStarted}</span>
                      <span>進行中: {stats.overall.inProgress}</span>
                      <span>完了: {stats.overall.completed}</span>
                    </div>
                  </div>

                  {/* 親タスク別進捗 */}
                  {Object.entries(stats.byParent).map(([parentId, parentStats]) => (
                    <div key={parentId}>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-700 truncate" title={parentStats.title}>
                          {parentStats.title.length > 20
                            ? `${parentStats.title.substring(0, 20)}...`
                            : parentStats.title}
                        </span>
                        <span className="text-sm font-medium text-gray-900">
                          {parentStats.completed}/{parentStats.total} 完了
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{
                            width: `${parentStats.total > 0
                              ? (parentStats.completed / parentStats.total) * 100
                              : 0}%`
                          }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 text-gray-700">データを取得できませんでした</div>
              )}
          </div>

          {/* 収支サマリー */}
          <div className="card">
            <h2>収支サマリー</h2>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-600">年収目標</span>
                <span style={{ color: '#48bb78', fontWeight: '600' }}>800-1000万円</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">月間生活費</span>
                <span style={{ color: '#f56565', fontWeight: '600' }}>30万円</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">現在資産</span>
                <span style={{ color: '#667eea', fontWeight: '600' }}>2500万円</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">予想余剰分</span>
                <span style={{ color: '#48bb78', fontWeight: '600' }}>200-340万円/年</span>
              </div>
            </div>
          </div>
        </div>

        {/* クイックアクション */}
        <div className="mt-8 fade-in">
          <h2>クイックアクション</h2>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <a href="/tasks" className="card" style={{ textDecoration: 'none', color: 'inherit' }}>
              <h3>タスク管理</h3>
              <p>進捗状況の確認と更新</p>
            </a>
            <a href="/finance" className="card" style={{ textDecoration: 'none', color: 'inherit' }}>
              <h3>収支管理</h3>
              <p>収支計画の確認と更新</p>
            </a>
            <button
              className="card"
              onClick={() => window.location.reload()}
              style={{ textAlign: 'left', border: 'none', background: 'transparent' }}
            >
              <h3>データ更新</h3>
              <p>最新情報を取得</p>
            </button>
          </div>
        </div>
      </main>
    </div>
  );
}
