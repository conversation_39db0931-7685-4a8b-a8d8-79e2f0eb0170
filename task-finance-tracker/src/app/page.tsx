import Header from '@/components/Header';

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">ダッシュボード</h1>
          <p className="mt-2 text-gray-600">
            退職・移住計画の進捗状況と収支管理の概要
          </p>
        </div>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* タスク進捗サマリー */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">
                タスク進捗サマリー
              </h2>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">7月のタスク</span>
                  <span className="text-sm font-medium text-gray-900">0/4 完了</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full" style={{ width: '0%' }}></div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">8月のタスク</span>
                  <span className="text-sm font-medium text-gray-900">0/4 完了</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full" style={{ width: '0%' }}></div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">9月のタスク</span>
                  <span className="text-sm font-medium text-gray-900">0/4 完了</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full" style={{ width: '0%' }}></div>
                </div>
              </div>
            </div>
          </div>

          {/* 収支サマリー */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">
                収支サマリー
              </h2>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">年収目標</span>
                  <span className="text-sm font-medium text-green-600">800-1000万円</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">月間生活費</span>
                  <span className="text-sm font-medium text-red-600">30万円</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">現在資産</span>
                  <span className="text-sm font-medium text-blue-600">2500万円</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">予想余剰分</span>
                  <span className="text-sm font-medium text-green-600">200-340万円/年</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* クイックアクション */}
        <div className="mt-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">クイックアクション</h2>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <a
              href="/tasks"
              className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow"
            >
              <h3 className="text-lg font-medium text-gray-900">タスク管理</h3>
              <p className="mt-2 text-sm text-gray-600">
                進捗状況の確認と更新
              </p>
            </a>
            <a
              href="/finance"
              className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow"
            >
              <h3 className="text-lg font-medium text-gray-900">収支管理</h3>
              <p className="mt-2 text-sm text-gray-600">
                収支計画の確認と更新
              </p>
            </a>
            <button
              className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow text-left"
              onClick={() => window.location.reload()}
            >
              <h3 className="text-lg font-medium text-gray-900">データ更新</h3>
              <p className="mt-2 text-sm text-gray-600">
                最新情報を取得
              </p>
            </button>
          </div>
        </div>
      </main>
    </div>
  );
}
