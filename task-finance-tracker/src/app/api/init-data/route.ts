import { NextResponse } from 'next/server';
import { KVClient, Task, FinancialPlan } from '@/lib/kv';
import { initialTasks, initialFinancialPlans } from '@/lib/initial-data';

export async function POST() {
  // 開発環境でのみ実行可能
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'This endpoint is only available in development mode' },
      { status: 403 }
    );
  }

  try {
    // 既存データをチェック
    const existingTasks = await KVClient.getTasks();
    const existingPlans = await KVClient.getFinancialPlans();

    if (existingTasks.length > 0 || existingPlans.length > 0) {
      return NextResponse.json(
        { error: 'Data already exists. Use force=true to overwrite.' },
        { status: 400 }
      );
    }

    // タスクデータの初期化
    const tasksWithIds: Task[] = [];
    for (const taskData of initialTasks) {
      const id = await KVClient.getNextTaskId();
      tasksWithIds.push({
        ...taskData,
        id,
        // parentIdの調整（実際のIDに変換）
        parentId: taskData.parentId ? `task_${taskData.parentId.split('_')[1]}` : undefined,
      });
    }

    // 収支計画データの初期化
    const plansWithIds: FinancialPlan[] = [];
    for (const planData of initialFinancialPlans) {
      const id = await KVClient.getNextFinancialId();
      plansWithIds.push({
        ...planData,
        id,
      });
    }

    // データ保存
    await KVClient.saveTasks(tasksWithIds);
    await KVClient.saveFinancialPlans(plansWithIds);

    return NextResponse.json({
      success: true,
      message: 'Initial data has been created successfully',
      data: {
        tasksCount: tasksWithIds.length,
        plansCount: plansWithIds.length,
      },
    });
  } catch (error) {
    console.error('Error initializing data:', error);
    return NextResponse.json(
      { error: 'Failed to initialize data' },
      { status: 500 }
    );
  }
}

// 強制初期化（既存データを削除して再作成）
export async function PUT() {
  // 開発環境でのみ実行可能
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'This endpoint is only available in development mode' },
      { status: 403 }
    );
  }

  try {
    // 既存データをクリア
    await KVClient.clearAllData();

    // 新しいデータを作成
    const tasksWithIds: Task[] = [];
    for (const taskData of initialTasks) {
      const id = await KVClient.getNextTaskId();
      tasksWithIds.push({
        ...taskData,
        id,
        parentId: taskData.parentId ? `task_${taskData.parentId.split('_')[1]}` : undefined,
      });
    }

    const plansWithIds: FinancialPlan[] = [];
    for (const planData of initialFinancialPlans) {
      const id = await KVClient.getNextFinancialId();
      plansWithIds.push({
        ...planData,
        id,
      });
    }

    await KVClient.saveTasks(tasksWithIds);
    await KVClient.saveFinancialPlans(plansWithIds);

    return NextResponse.json({
      success: true,
      message: 'Data has been reset and initialized successfully',
      data: {
        tasksCount: tasksWithIds.length,
        plansCount: plansWithIds.length,
      },
    });
  } catch (error) {
    console.error('Error resetting data:', error);
    return NextResponse.json(
      { error: 'Failed to reset data' },
      { status: 500 }
    );
  }
}
