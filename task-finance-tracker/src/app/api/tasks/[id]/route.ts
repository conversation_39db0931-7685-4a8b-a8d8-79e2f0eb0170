import { NextRequest, NextResponse } from 'next/server';
import { KVClient, Task } from '@/lib/kv';
import { validateSession } from '@/lib/auth';

// 個別タスク取得
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 認証チェック
    const isAuthenticated = await validateSession();
    if (!isAuthenticated) {
      return NextResponse.json(
        { error: '認証が必要です' },
        { status: 401 }
      );
    }

    const tasks = await KVClient.getTasks();
    const task = tasks.find(t => t.id === params.id);

    if (!task) {
      return NextResponse.json(
        { error: 'タスクが見つかりません' },
        { status: 404 }
      );
    }

    return NextResponse.json({ task });
  } catch (error) {
    console.error('Error fetching task:', error);
    return NextResponse.json(
      { error: 'タスクの取得に失敗しました' },
      { status: 500 }
    );
  }
}

// タスク更新
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 認証チェック
    const isAuthenticated = await validateSession();
    if (!isAuthenticated) {
      return NextResponse.json(
        { error: '認証が必要です' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { title, description, status, targetDate, priority, financialImpact } = body;

    // バリデーション
    if (title !== undefined && (!title || !title.trim())) {
      return NextResponse.json(
        { error: 'タイトルは必須です' },
        { status: 400 }
      );
    }

    if (status && !['not_started', 'in_progress', 'completed'].includes(status)) {
      return NextResponse.json(
        { error: 'ステータスは not_started, in_progress, completed のいずれかである必要があります' },
        { status: 400 }
      );
    }

    if (priority && !['low', 'medium', 'high'].includes(priority)) {
      return NextResponse.json(
        { error: '優先度は low, medium, high のいずれかである必要があります' },
        { status: 400 }
      );
    }

    const tasks = await KVClient.getTasks();
    const taskIndex = tasks.findIndex(t => t.id === params.id);

    if (taskIndex === -1) {
      return NextResponse.json(
        { error: 'タスクが見つかりません' },
        { status: 404 }
      );
    }

    // タスクを更新
    const updatedTask: Task = {
      ...tasks[taskIndex],
      ...(title !== undefined && { title: title.trim() }),
      ...(description !== undefined && { description: description?.trim() || undefined }),
      ...(status !== undefined && { status }),
      ...(targetDate !== undefined && { targetDate: targetDate || undefined }),
      ...(priority !== undefined && { priority }),
      ...(financialImpact !== undefined && { financialImpact }),
      updatedAt: new Date().toISOString(),
    };

    tasks[taskIndex] = updatedTask;
    await KVClient.saveTasks(tasks);

    return NextResponse.json({ task: updatedTask });
  } catch (error) {
    console.error('Error updating task:', error);
    return NextResponse.json(
      { error: 'タスクの更新に失敗しました' },
      { status: 500 }
    );
  }
}

// タスク削除
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 認証チェック
    const isAuthenticated = await validateSession();
    if (!isAuthenticated) {
      return NextResponse.json(
        { error: '認証が必要です' },
        { status: 401 }
      );
    }

    const tasks = await KVClient.getTasks();
    const taskIndex = tasks.findIndex(t => t.id === params.id);

    if (taskIndex === -1) {
      return NextResponse.json(
        { error: 'タスクが見つかりません' },
        { status: 404 }
      );
    }

    // 子タスクがある場合は削除を拒否
    const hasChildren = tasks.some(t => t.parentId === params.id);
    if (hasChildren) {
      return NextResponse.json(
        { error: '子タスクが存在するため削除できません。先に子タスクを削除してください。' },
        { status: 400 }
      );
    }

    // タスクを削除
    const deletedTask = tasks[taskIndex];
    tasks.splice(taskIndex, 1);
    await KVClient.saveTasks(tasks);

    return NextResponse.json({ 
      message: 'タスクが削除されました',
      task: deletedTask 
    });
  } catch (error) {
    console.error('Error deleting task:', error);
    return NextResponse.json(
      { error: 'タスクの削除に失敗しました' },
      { status: 500 }
    );
  }
}
