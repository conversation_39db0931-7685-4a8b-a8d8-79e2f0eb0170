import { NextRequest, NextResponse } from 'next/server';
import { KVClient, Task } from '@/lib/kv';
import { validateSession } from '@/lib/auth';

// タスク一覧取得
export async function GET(request: NextRequest) {
  try {
    // 認証チェック
    const isAuthenticated = await validateSession();
    if (!isAuthenticated) {
      return NextResponse.json(
        { error: '認証が必要です' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const parentId = searchParams.get('parentId');
    const status = searchParams.get('status');
    const priority = searchParams.get('priority');

    let tasks = await KVClient.getTasks();

    // フィルタリング
    if (parentId) {
      tasks = tasks.filter(task => task.parentId === parentId);
    }
    if (status) {
      tasks = tasks.filter(task => task.status === status);
    }
    if (priority) {
      tasks = tasks.filter(task => task.priority === priority);
    }

    // 作成日時でソート
    tasks.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());

    return NextResponse.json({ tasks });
  } catch (error) {
    console.error('Error fetching tasks:', error);
    return NextResponse.json(
      { error: 'タスクの取得に失敗しました' },
      { status: 500 }
    );
  }
}

// タスク作成
export async function POST(request: NextRequest) {
  try {
    // 認証チェック
    const isAuthenticated = await validateSession();
    if (!isAuthenticated) {
      return NextResponse.json(
        { error: '認証が必要です' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { title, description, targetDate, priority, parentId, financialImpact } = body;

    // バリデーション
    if (!title || !title.trim()) {
      return NextResponse.json(
        { error: 'タイトルは必須です' },
        { status: 400 }
      );
    }

    if (priority && !['low', 'medium', 'high'].includes(priority)) {
      return NextResponse.json(
        { error: '優先度は low, medium, high のいずれかである必要があります' },
        { status: 400 }
      );
    }

    // 新しいタスクを作成
    const id = await KVClient.getNextTaskId();
    const now = new Date().toISOString();

    const newTask: Task = {
      id,
      title: title.trim(),
      description: description?.trim() || undefined,
      status: 'not_started',
      targetDate: targetDate || undefined,
      priority: priority || 'medium',
      parentId: parentId || undefined,
      financialImpact: financialImpact || undefined,
      createdAt: now,
      updatedAt: now,
    };

    // 既存のタスクを取得して新しいタスクを追加
    const tasks = await KVClient.getTasks();
    tasks.push(newTask);
    await KVClient.saveTasks(tasks);

    return NextResponse.json({ task: newTask }, { status: 201 });
  } catch (error) {
    console.error('Error creating task:', error);
    return NextResponse.json(
      { error: 'タスクの作成に失敗しました' },
      { status: 500 }
    );
  }
}
