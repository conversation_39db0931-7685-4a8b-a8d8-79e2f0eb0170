import { NextRequest, NextResponse } from 'next/server';
import { KVClient, Task } from '@/lib/kv';
import { validateSession } from '@/lib/auth';

interface TaskWithChildren extends Task {
  children: TaskWithChildren[];
}

// 階層構造でタスクを取得
export async function GET(request: NextRequest) {
  try {
    // 認証チェック
    const isAuthenticated = await validateSession();
    if (!isAuthenticated) {
      return NextResponse.json(
        { error: '認証が必要です' },
        { status: 401 }
      );
    }

    const tasks = await KVClient.getTasks();
    
    // 階層構造を構築
    const taskMap = new Map<string, TaskWithChildren>();
    const rootTasks: TaskWithChildren[] = [];

    // 全タスクをマップに追加
    tasks.forEach(task => {
      taskMap.set(task.id, { ...task, children: [] });
    });

    // 階層構造を構築
    tasks.forEach(task => {
      const taskWithChildren = taskMap.get(task.id)!;
      
      if (task.parentId) {
        const parent = taskMap.get(task.parentId);
        if (parent) {
          parent.children.push(taskWithChildren);
        } else {
          // 親が見つからない場合はルートタスクとして扱う
          rootTasks.push(taskWithChildren);
        }
      } else {
        rootTasks.push(taskWithChildren);
      }
    });

    // 各レベルで作成日時順にソート
    const sortTasks = (tasks: TaskWithChildren[]) => {
      tasks.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
      tasks.forEach(task => {
        if (task.children.length > 0) {
          sortTasks(task.children);
        }
      });
    };

    sortTasks(rootTasks);

    return NextResponse.json({ tasks: rootTasks });
  } catch (error) {
    console.error('Error fetching task hierarchy:', error);
    return NextResponse.json(
      { error: 'タスク階層の取得に失敗しました' },
      { status: 500 }
    );
  }
}

// 進捗統計を取得
export async function POST(request: NextRequest) {
  try {
    // 認証チェック
    const isAuthenticated = await validateSession();
    if (!isAuthenticated) {
      return NextResponse.json(
        { error: '認証が必要です' },
        { status: 401 }
      );
    }

    const tasks = await KVClient.getTasks();
    
    // 全体統計
    const totalTasks = tasks.length;
    const completedTasks = tasks.filter(t => t.status === 'completed').length;
    const inProgressTasks = tasks.filter(t => t.status === 'in_progress').length;
    const notStartedTasks = tasks.filter(t => t.status === 'not_started').length;

    // 親タスク別統計
    const parentStats = new Map<string, {
      total: number;
      completed: number;
      inProgress: number;
      notStarted: number;
      title: string;
    }>();

    // ルートタスクの統計
    const rootTasks = tasks.filter(t => !t.parentId);
    rootTasks.forEach(rootTask => {
      const childTasks = tasks.filter(t => t.parentId === rootTask.id);
      const total = childTasks.length;
      const completed = childTasks.filter(t => t.status === 'completed').length;
      const inProgress = childTasks.filter(t => t.status === 'in_progress').length;
      const notStarted = childTasks.filter(t => t.status === 'not_started').length;

      parentStats.set(rootTask.id, {
        total,
        completed,
        inProgress,
        notStarted,
        title: rootTask.title,
      });
    });

    // 優先度別統計
    const priorityStats = {
      high: tasks.filter(t => t.priority === 'high').length,
      medium: tasks.filter(t => t.priority === 'medium').length,
      low: tasks.filter(t => t.priority === 'low').length,
    };

    // 期限別統計（今月、来月、それ以降）
    const now = new Date();
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
    const monthAfterNext = new Date(now.getFullYear(), now.getMonth() + 2, 1);

    const dueDateStats = {
      thisMonth: tasks.filter(t => {
        if (!t.targetDate) return false;
        const targetDate = new Date(t.targetDate);
        return targetDate >= thisMonth && targetDate < nextMonth;
      }).length,
      nextMonth: tasks.filter(t => {
        if (!t.targetDate) return false;
        const targetDate = new Date(t.targetDate);
        return targetDate >= nextMonth && targetDate < monthAfterNext;
      }).length,
      later: tasks.filter(t => {
        if (!t.targetDate) return false;
        const targetDate = new Date(t.targetDate);
        return targetDate >= monthAfterNext;
      }).length,
      noDate: tasks.filter(t => !t.targetDate).length,
    };

    return NextResponse.json({
      overall: {
        total: totalTasks,
        completed: completedTasks,
        inProgress: inProgressTasks,
        notStarted: notStartedTasks,
        completionRate: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
      },
      byParent: Object.fromEntries(parentStats),
      byPriority: priorityStats,
      byDueDate: dueDateStats,
    });
  } catch (error) {
    console.error('Error fetching task statistics:', error);
    return NextResponse.json(
      { error: 'タスク統計の取得に失敗しました' },
      { status: 500 }
    );
  }
}
