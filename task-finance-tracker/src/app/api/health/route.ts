import { NextResponse } from 'next/server';
import { KVClient } from '@/lib/kv';

export async function GET() {
  try {
    const isHealthy = await KVClient.healthCheck();
    
    if (isHealthy) {
      return NextResponse.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        kv: 'connected',
      });
    } else {
      return NextResponse.json(
        {
          status: 'unhealthy',
          timestamp: new Date().toISOString(),
          kv: 'disconnected',
        },
        { status: 503 }
      );
    }
  } catch (error) {
    console.error('Health check error:', error);
    return NextResponse.json(
      {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
      },
      { status: 500 }
    );
  }
}
